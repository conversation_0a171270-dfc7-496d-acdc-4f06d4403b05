2025-05-27 09:03:01,964 - __main__ - INFO - Starting Red.ai RAG Engine on PORT 8000...
2025-05-27 09:03:01,964 - __main__ - INFO - Starting Huey worker subprocess...
2025-05-27 09:03:01,969 - __main__ - INFO - Worker started with PID: 10788
2025-05-27 09:03:01,969 - __main__ - INFO - Waiting for worker to initialize...
2025-05-27 09:03:04,969 - __main__ - INFO - Starting FastAPI server on port 8000...
2025-05-27 09:03:07,173 - services.queue_service - INFO - Huey queue initialized with SQLite database: queue_data\huey_queue.db
2025-05-27 09:09:38,777 - __main__ - INFO - Starting Red.ai RAG Engine on PORT 8000...
2025-05-27 09:09:38,778 - __main__ - INFO - Starting Huey worker subprocess...
2025-05-27 09:09:38,783 - __main__ - INFO - Worker started with PID: 14260
2025-05-27 09:09:38,783 - __main__ - INFO - Waiting for worker to initialize...
2025-05-27 09:09:41,783 - __main__ - INFO - Starting FastAPI server on port 8000...
2025-05-27 09:09:44,888 - services.queue_service - INFO - Huey queue initialized with SQLite database: queue_data\huey_queue.db
2025-05-27 09:11:13,604 - __main__ - INFO - Starting Red.ai RAG Engine on PORT 8000...
2025-05-27 09:11:13,605 - __main__ - INFO - Starting Huey worker subprocess...
2025-05-27 09:11:13,609 - __main__ - INFO - Worker started with PID: 19808
2025-05-27 09:11:13,609 - __main__ - INFO - Waiting for worker to initialize...
2025-05-27 09:11:16,610 - __main__ - INFO - Starting FastAPI server on port 8000...
2025-05-27 09:11:18,674 - services.queue_service - INFO - Huey queue initialized with SQLite database: queue_data\huey_queue.db
