2025-05-21 14:50:00 - main - INFO - [main.py:82] - Starting up the application
2025-05-21 14:50:00 - main - INFO - [main.py:86] - Ensured temp directory exists: temp-s3
2025-05-21 14:50:00 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-21 14:50:00 - main - INFO - [main.py:91] - Database initialized successfully
2025-05-21 14:50:34 - main - INFO - [main.py:148] - Request to /docs completed in 0.0003s
2025-05-21 14:50:42 - main - INFO - [main.py:99] - Shutting down the application
2025-05-21 14:53:01 - main - INFO - [main.py:82] - Starting up the application
2025-05-21 14:53:01 - main - INFO - [main.py:86] - Ensured temp directory exists: temp-s3
2025-05-21 14:53:01 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-21 14:53:01 - main - INFO - [main.py:91] - Database initialized successfully
2025-05-21 14:53:23 - main - INFO - [main.py:148] - Request to /docs completed in 0.0006s
2025-05-21 14:53:23 - main - INFO - [main.py:148] - Request to /openapi.json completed in 0.0089s
2025-05-21 14:53:49 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-Z1G-2i6SHoybAm-q, 34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano%20Ronaldo
2025-05-21 14:53:49 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-Z1G-2i6SHoybAm-q: 0% - Đang bắt đầu xử lý tập tin
2025-05-21 14:53:49 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-Z1G-2i6SHoybAm-q: 5% - Đang tải tập tin
2025-05-21 14:53:49 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano20Ronaldo
2025-05-21 14:53:49 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-Z1G-2i6SHoybAm-q_83703d6e-c821-4f40-891b-b87449452899
2025-05-21 14:53:49 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-Z1G-2i6SHoybAm-q_83703d6e-c821-4f40-891b-b87449452899
2025-05-21 14:53:49 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-Z1G-2i6SHoybAm-q_83703d6e-c821-4f40-891b-b87449452899
2025-05-21 14:53:49 - main - INFO - [main.py:148] - Request to /api/api/files/process-url completed in 0.7772s
2025-05-21 14:53:49 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-Z1G-2i6SHoybAm-q: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-21 14:53:49 - services.markdown_service - INFO - [markdown_service.py:26] - Converting file to Markdown: temp-s3\temp_file-Z1G-2i6SHoybAm-q_83703d6e-c821-4f40-891b-b87449452899 (type: )
2025-05-21 14:53:49 - services.markdown_service - WARNING - [markdown_service.py:71] - Unsupported file type: , attempting generic conversion
2025-05-21 14:53:49 - services.markdown_service - INFO - [markdown_service.py:82] - Successfully converted file to Markdown (1369 characters)
2025-05-21 14:53:49 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-Z1G-2i6SHoybAm-q: 30% - Đang chia nhỏ nội dung
2025-05-21 14:53:49 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-21 14:53:49 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 1 well-sized chunks
2025-05-21 14:53:49 - services.document_chunks_service - INFO - [document_chunks_service.py:65] - Split content into 1 chunks for file file-Z1G-2i6SHoybAm-q
2025-05-21 14:53:49 - services.document_chunks_service - ERROR - [document_chunks_service.py:97] - Database error creating chunks: (psycopg2.errors.ForeignKeyViolation) insert or update on table "document_chunks" violates foreign key constraint "document_chunks_file_id_fkey"
DETAIL:  Key (file_id)=(file-Z1G-2i6SHoybAm-q) is not present in table "file".

[SQL: INSERT INTO document_chunks (id, file_id, chunk_index, content, chunk_metadata, embedding, embedding_source, embedding_dimensions, created_at, updated_at) VALUES (%(id)s::UUID, %(file_id)s, %(chunk_index)s, %(content)s, %(chunk_metadata)s::JSONB, %(embedding)s, %(embedding_source)s, %(embedding_dimensions)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('e16174ce-acf8-4432-90a4-ae32be6244ac'), 'file_id': 'file-Z1G-2i6SHoybAm-q', 'chunk_index': 0, 'content': 'TUYỂN DỤNG THỰC TẬP SINH BACKEND\n\nCông ty CP Đầu tư và Thương mại Redon đang tìm kiếm Thực tập sinh Backend tiềm\n\nnăng để cùng đồng hành và phát  ... (1222 characters truncated) ... quan nếu có) về: <EMAIL>\n\nvới tiêu đề ghi rõ: [TTS Frontend] – Họ và Tên\n\n\uf0fc\n\nHoặc liên hệ qua Zalo bằng số điện thoại: 0865475477', 'chunk_metadata': '{}', 'embedding': None, 'embedding_source': 'openai', 'embedding_dimensions': 1536, 'created_at': 1747814029837, 'updated_at': 1747814029837}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-21 14:53:49 - api.file_endpoints - ERROR - [file_endpoints.py:163] - Error processing file file-Z1G-2i6SHoybAm-q: (psycopg2.errors.ForeignKeyViolation) insert or update on table "document_chunks" violates foreign key constraint "document_chunks_file_id_fkey"
DETAIL:  Key (file_id)=(file-Z1G-2i6SHoybAm-q) is not present in table "file".

[SQL: INSERT INTO document_chunks (id, file_id, chunk_index, content, chunk_metadata, embedding, embedding_source, embedding_dimensions, created_at, updated_at) VALUES (%(id)s::UUID, %(file_id)s, %(chunk_index)s, %(content)s, %(chunk_metadata)s::JSONB, %(embedding)s, %(embedding_source)s, %(embedding_dimensions)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('e16174ce-acf8-4432-90a4-ae32be6244ac'), 'file_id': 'file-Z1G-2i6SHoybAm-q', 'chunk_index': 0, 'content': 'TUYỂN DỤNG THỰC TẬP SINH BACKEND\n\nCông ty CP Đầu tư và Thương mại Redon đang tìm kiếm Thực tập sinh Backend tiềm\n\nnăng để cùng đồng hành và phát  ... (1222 characters truncated) ... quan nếu có) về: <EMAIL>\n\nvới tiêu đề ghi rõ: [TTS Frontend] – Họ và Tên\n\n\uf0fc\n\nHoặc liên hệ qua Zalo bằng số điện thoại: 0865475477', 'chunk_metadata': '{}', 'embedding': None, 'embedding_source': 'openai', 'embedding_dimensions': 1536, 'created_at': 1747814029837, 'updated_at': 1747814029837}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-21 14:53:49 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-Z1G-2i6SHoybAm-q: 100% - Lỗi xử lý tập tin
2025-05-21 14:53:49 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-Z1G-2i6SHoybAm-q_83703d6e-c821-4f40-891b-b87449452899
2025-05-21 14:55:22 - main - INFO - [main.py:99] - Shutting down the application
2025-05-21 14:56:36 - main - INFO - [main.py:82] - Starting up the application
2025-05-21 14:56:36 - main - INFO - [main.py:86] - Ensured temp directory exists: temp-s3
2025-05-21 14:56:36 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-21 14:56:36 - main - INFO - [main.py:91] - Database initialized successfully
2025-05-21 14:56:53 - services.s3_file_service - WARNING - [s3_file_service.py:58] - URL not accessible: https://cdn.redai.vn/34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano%20Ronaldo, status code: 404
2025-05-21 14:56:53 - main - INFO - [main.py:148] - Request to /api/api/files/process-url completed in 0.2748s
2025-05-21 14:57:05 - services.s3_file_service - WARNING - [s3_file_service.py:58] - URL not accessible: https://cdn.redai.vn/sample-document.pdf, status code: 404
2025-05-21 14:57:05 - main - INFO - [main.py:148] - Request to /api/api/files/process-url completed in 0.2451s
2025-05-21 14:57:12 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file--rGxmzdkR489T8YT, dummy.pdf
2025-05-21 14:57:12 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file--rGxmzdkR489T8YT: 0% - Đang bắt đầu xử lý tập tin
2025-05-21 14:57:12 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file--rGxmzdkR489T8YT: 5% - Đang tải tập tin
2025-05-21 14:57:12 - core.s3_client - INFO - [s3_client.py:82] - Downloading from R2: dummy.pdf to temp_file--rGxmzdkR489T8YT_c124d339-caaf-45f7-8406-53d384ae45b7.pdf
2025-05-21 14:57:13 - core.s3_client - ERROR - [s3_client.py:105] - S3 client error downloading dummy.pdf: An error occurred (404) when calling the HeadObject operation: Not Found
2025-05-21 14:57:13 - services.s3_file_service - ERROR - [s3_file_service.py:198] - Error downloading file to temp: Failed to download file from storage: An error occurred (404) when calling the HeadObject operation: Not Found
2025-05-21 14:57:13 - api.file_endpoints - ERROR - [file_endpoints.py:256] - Error processing URL: Failed to download file from storage: An error occurred (404) when calling the HeadObject operation: Not Found
2025-05-21 14:57:13 - main - INFO - [main.py:148] - Request to /api/api/files/process-url completed in 1.7010s
2025-05-21 14:58:25 - main - INFO - [main.py:99] - Shutting down the application
2025-05-21 14:59:15 - main - INFO - [main.py:82] - Starting up the application
2025-05-21 14:59:15 - main - INFO - [main.py:86] - Ensured temp directory exists: temp-s3
2025-05-21 14:59:15 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-21 14:59:15 - main - INFO - [main.py:91] - Database initialized successfully
2025-05-21 14:59:49 - main - INFO - [main.py:148] - Request to /docs completed in 0.0004s
2025-05-21 14:59:49 - main - INFO - [main.py:148] - Request to /openapi.json completed in 0.0165s
2025-05-21 14:59:56 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-fJerFlCOkdqTEy73, 34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano%20Ronaldo
2025-05-21 14:59:56 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-fJerFlCOkdqTEy73: 0% - Đang bắt đầu xử lý tập tin
2025-05-21 14:59:56 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-fJerFlCOkdqTEy73: 5% - Đang tải tập tin
2025-05-21 14:59:56 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano20Ronaldo
2025-05-21 14:59:56 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-fJerFlCOkdqTEy73_23802357-fad1-4308-9a90-1dfc282d4633
2025-05-21 14:59:56 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-fJerFlCOkdqTEy73_23802357-fad1-4308-9a90-1dfc282d4633
2025-05-21 14:59:56 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-fJerFlCOkdqTEy73_23802357-fad1-4308-9a90-1dfc282d4633
2025-05-21 14:59:56 - main - INFO - [main.py:148] - Request to /api/api/files/process-url completed in 0.4716s
2025-05-21 14:59:56 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-fJerFlCOkdqTEy73: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-21 14:59:56 - services.markdown_service - INFO - [markdown_service.py:26] - Converting file to Markdown: temp-s3\temp_file-fJerFlCOkdqTEy73_23802357-fad1-4308-9a90-1dfc282d4633 (type: )
2025-05-21 14:59:56 - services.markdown_service - WARNING - [markdown_service.py:71] - Unsupported file type: , attempting generic conversion
2025-05-21 14:59:57 - services.markdown_service - INFO - [markdown_service.py:82] - Successfully converted file to Markdown (1369 characters)
2025-05-21 14:59:57 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-fJerFlCOkdqTEy73: 30% - Đang chia nhỏ nội dung
2025-05-21 14:59:57 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-21 14:59:57 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 1 well-sized chunks
2025-05-21 14:59:57 - services.document_chunks_service - INFO - [document_chunks_service.py:65] - Split content into 1 chunks for file file-fJerFlCOkdqTEy73
2025-05-21 14:59:57 - services.document_chunks_service - INFO - [document_chunks_service.py:92] - Successfully saved 1 chunks for file file-fJerFlCOkdqTEy73
2025-05-21 14:59:57 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-fJerFlCOkdqTEy73: 50% - Đang tạo embeddings cho 1 đoạn
2025-05-21 14:59:57 - services.document_chunks_service - INFO - [document_chunks_service.py:141] - Creating embeddings for 1 chunks with 5 parallel workers
2025-05-21 14:59:57 - services.document_chunks_service - INFO - [document_chunks_service.py:146] - Processing batch 1 (1 chunks)
2025-05-21 14:59:57 - services.document_chunks_service - ERROR - [document_chunks_service.py:204] - Error creating embeddings in parallel: name 'get_embeddings_parallel' is not defined
2025-05-21 14:59:57 - services.document_chunks_service - INFO - [document_chunks_service.py:207] - Falling back to sequential embedding creation
2025-05-21 14:59:57 - services.document_chunks_service - ERROR - [document_chunks_service.py:234] - Error creating embedding for chunk a3f80761-8ea4-46d2-8860-dda8a2e35021: name 'get_embedding_with_source' is not defined
2025-05-21 14:59:57 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-fJerFlCOkdqTEy73: 100% - Hoàn thành xử lý tập tin với 0/1 đoạn
2025-05-21 14:59:57 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-fJerFlCOkdqTEy73_23802357-fad1-4308-9a90-1dfc282d4633
2025-05-21 15:06:27 - main - INFO - [main.py:99] - Shutting down the application
2025-05-21 15:06:29 - main - INFO - [main.py:82] - Starting up the application
2025-05-21 15:06:29 - main - INFO - [main.py:86] - Ensured temp directory exists: temp-s3
2025-05-21 15:06:29 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-21 15:06:29 - main - INFO - [main.py:91] - Database initialized successfully
2025-05-21 15:06:35 - main - INFO - [main.py:82] - Starting up the application
2025-05-21 15:06:35 - main - INFO - [main.py:86] - Ensured temp directory exists: temp-s3
2025-05-21 15:06:35 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-21 15:06:35 - main - INFO - [main.py:91] - Database initialized successfully
2025-05-21 15:06:57 - main - INFO - [main.py:148] - Request to /docs completed in 0.0006s
2025-05-21 15:06:57 - main - INFO - [main.py:148] - Request to /openapi.json completed in 0.0109s
2025-05-21 15:07:18 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-zy0rZeqNV3zQ0II1, 34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano%20Ronaldo
2025-05-21 15:07:18 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-zy0rZeqNV3zQ0II1: 0% - Đang bắt đầu xử lý tập tin
2025-05-21 15:07:18 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-zy0rZeqNV3zQ0II1: 5% - Đang tải tập tin
2025-05-21 15:07:18 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano20Ronaldo
2025-05-21 15:07:18 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-zy0rZeqNV3zQ0II1_a80370cb-9ed8-4c64-8bf8-f24be54ba23b
2025-05-21 15:07:18 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-zy0rZeqNV3zQ0II1_a80370cb-9ed8-4c64-8bf8-f24be54ba23b
2025-05-21 15:07:18 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-zy0rZeqNV3zQ0II1_a80370cb-9ed8-4c64-8bf8-f24be54ba23b
2025-05-21 15:07:18 - main - INFO - [main.py:148] - Request to /api/api/files/process-url completed in 0.2388s
2025-05-21 15:07:18 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-zy0rZeqNV3zQ0II1: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-21 15:07:18 - services.markdown_service - INFO - [markdown_service.py:26] - Converting file to Markdown: temp-s3\temp_file-zy0rZeqNV3zQ0II1_a80370cb-9ed8-4c64-8bf8-f24be54ba23b (type: )
2025-05-21 15:07:18 - services.markdown_service - WARNING - [markdown_service.py:71] - Unsupported file type: , attempting generic conversion
2025-05-21 15:07:18 - services.markdown_service - INFO - [markdown_service.py:82] - Successfully converted file to Markdown (1369 characters)
2025-05-21 15:07:18 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-zy0rZeqNV3zQ0II1: 30% - Đang chia nhỏ nội dung
2025-05-21 15:07:18 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-21 15:07:18 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 1 well-sized chunks
2025-05-21 15:07:18 - services.document_chunks_service - INFO - [document_chunks_service.py:65] - Split content into 1 chunks for file file-zy0rZeqNV3zQ0II1
2025-05-21 15:07:18 - services.document_chunks_service - INFO - [document_chunks_service.py:92] - Successfully saved 1 chunks for file file-zy0rZeqNV3zQ0II1
2025-05-21 15:07:18 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-zy0rZeqNV3zQ0II1: 50% - Đang tạo embeddings cho 1 đoạn
2025-05-21 15:07:18 - services.document_chunks_service - INFO - [document_chunks_service.py:141] - Creating embeddings for 1 chunks with 5 parallel workers
2025-05-21 15:07:18 - services.document_chunks_service - INFO - [document_chunks_service.py:146] - Processing batch 1 (1 chunks)
2025-05-21 15:07:20 - core.vector_store - ERROR - [vector_store.py:54] - Error getting Gemini embedding: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
2025-05-21 15:07:20 - core.vector_store - WARNING - [vector_store.py:127] - Gemini embedding failed, falling back to Jina: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
2025-05-21 15:07:21 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-zy0rZeqNV3zQ0II1: 100% - Hoàn thành xử lý tập tin với 1/1 đoạn
2025-05-21 15:07:21 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-zy0rZeqNV3zQ0II1_a80370cb-9ed8-4c64-8bf8-f24be54ba23b
2025-05-21 15:11:32 - main - INFO - [main.py:99] - Shutting down the application
2025-05-21 15:11:37 - main - INFO - [main.py:82] - Starting up the application
2025-05-21 15:11:37 - main - INFO - [main.py:86] - Ensured temp directory exists: temp-s3
2025-05-21 15:11:37 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-21 15:11:37 - main - INFO - [main.py:91] - Database initialized successfully
2025-05-21 15:14:21 - main - INFO - [main.py:82] - Starting up the application
2025-05-21 15:14:21 - main - INFO - [main.py:86] - Ensured temp directory exists: temp-s3
2025-05-21 15:14:21 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-21 15:14:21 - main - INFO - [main.py:91] - Database initialized successfully
2025-05-21 15:15:48 - main - INFO - [main.py:99] - Shutting down the application
2025-05-21 15:15:48 - main - INFO - [main.py:99] - Shutting down the application
2025-05-21 15:16:42 - main - INFO - [main.py:82] - Starting up the application
2025-05-21 15:16:42 - main - INFO - [main.py:86] - Ensured temp directory exists: temp-s3
2025-05-21 15:16:42 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-21 15:16:42 - main - INFO - [main.py:91] - Database initialized successfully
2025-05-21 15:17:04 - main - INFO - [main.py:148] - Request to /docs completed in 0.0005s
2025-05-21 15:17:04 - main - INFO - [main.py:148] - Request to /openapi.json completed in 0.0076s
2025-05-21 15:17:12 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-bDtihr1OJLb41jpz, 34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano%20Ronaldo
2025-05-21 15:17:12 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-bDtihr1OJLb41jpz: 0% - Đang bắt đầu xử lý tập tin
2025-05-21 15:17:12 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-bDtihr1OJLb41jpz: 5% - Đang tải tập tin
2025-05-21 15:17:12 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano20Ronaldo
2025-05-21 15:17:12 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-bDtihr1OJLb41jpz_dbbe762b-0caa-4854-9a78-fc4bbcea5d27
2025-05-21 15:17:12 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-bDtihr1OJLb41jpz_dbbe762b-0caa-4854-9a78-fc4bbcea5d27
2025-05-21 15:17:12 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-bDtihr1OJLb41jpz_dbbe762b-0caa-4854-9a78-fc4bbcea5d27
2025-05-21 15:17:12 - main - INFO - [main.py:148] - Request to /api/api/files/process-url completed in 0.9483s
2025-05-21 15:17:12 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-bDtihr1OJLb41jpz: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-21 15:17:12 - services.markdown_service - INFO - [markdown_service.py:26] - Converting file to Markdown: temp-s3\temp_file-bDtihr1OJLb41jpz_dbbe762b-0caa-4854-9a78-fc4bbcea5d27 (type: )
2025-05-21 15:17:12 - services.markdown_service - WARNING - [markdown_service.py:71] - Unsupported file type: , attempting generic conversion
2025-05-21 15:17:12 - services.markdown_service - INFO - [markdown_service.py:82] - Successfully converted file to Markdown (1369 characters)
2025-05-21 15:17:12 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-bDtihr1OJLb41jpz: 30% - Đang chia nhỏ nội dung
2025-05-21 15:17:12 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-21 15:17:12 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 1 well-sized chunks
2025-05-21 15:17:12 - services.document_chunks_service - INFO - [document_chunks_service.py:65] - Split content into 1 chunks for file file-bDtihr1OJLb41jpz
2025-05-21 15:17:12 - services.document_chunks_service - INFO - [document_chunks_service.py:92] - Successfully saved 1 chunks for file file-bDtihr1OJLb41jpz
2025-05-21 15:17:12 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-bDtihr1OJLb41jpz: 50% - Đang tạo embeddings cho 1 đoạn
2025-05-21 15:17:12 - services.document_chunks_service - INFO - [document_chunks_service.py:141] - Creating embeddings for 1 chunks with 5 parallel workers
2025-05-21 15:17:12 - services.document_chunks_service - INFO - [document_chunks_service.py:146] - Processing batch 1 (1 chunks)
2025-05-21 15:17:13 - core.vector_store - ERROR - [vector_store.py:89] - Error getting Gemini embedding: 'Model' object has no attribute 'embed_content'
2025-05-21 15:17:13 - core.vector_store - WARNING - [vector_store.py:170] - Gemini embedding failed, falling back to Jina: 'Model' object has no attribute 'embed_content'
2025-05-21 15:17:15 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-21 15:17:15 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-bDtihr1OJLb41jpz: 100% - Hoàn thành xử lý tập tin với 1/1 đoạn
2025-05-21 15:17:15 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-bDtihr1OJLb41jpz_dbbe762b-0caa-4854-9a78-fc4bbcea5d27
2025-05-21 15:18:12 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-Fv-k06gjpIlzWse3, 34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano%20Ronaldo
2025-05-21 15:18:12 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-Fv-k06gjpIlzWse3: 0% - Đang bắt đầu xử lý tập tin
2025-05-21 15:18:12 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-Fv-k06gjpIlzWse3: 5% - Đang tải tập tin
2025-05-21 15:18:12 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano20Ronaldo
2025-05-21 15:18:12 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-Fv-k06gjpIlzWse3_53f41a9d-589a-4384-a49d-0705b4eabb2b
2025-05-21 15:18:13 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-Fv-k06gjpIlzWse3_53f41a9d-589a-4384-a49d-0705b4eabb2b
2025-05-21 15:18:13 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-Fv-k06gjpIlzWse3_53f41a9d-589a-4384-a49d-0705b4eabb2b
2025-05-21 15:18:13 - main - INFO - [main.py:148] - Request to /api/api/files/process-url completed in 0.4085s
2025-05-21 15:18:13 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-Fv-k06gjpIlzWse3: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-21 15:18:13 - services.markdown_service - INFO - [markdown_service.py:26] - Converting file to Markdown: temp-s3\temp_file-Fv-k06gjpIlzWse3_53f41a9d-589a-4384-a49d-0705b4eabb2b (type: )
2025-05-21 15:18:13 - services.markdown_service - WARNING - [markdown_service.py:71] - Unsupported file type: , attempting generic conversion
2025-05-21 15:18:13 - services.markdown_service - INFO - [markdown_service.py:82] - Successfully converted file to Markdown (1369 characters)
2025-05-21 15:18:13 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-Fv-k06gjpIlzWse3: 30% - Đang chia nhỏ nội dung
2025-05-21 15:18:13 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-21 15:18:13 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 1 well-sized chunks
2025-05-21 15:18:13 - services.document_chunks_service - INFO - [document_chunks_service.py:65] - Split content into 1 chunks for file file-Fv-k06gjpIlzWse3
2025-05-21 15:18:13 - services.document_chunks_service - INFO - [document_chunks_service.py:92] - Successfully saved 1 chunks for file file-Fv-k06gjpIlzWse3
2025-05-21 15:18:13 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-Fv-k06gjpIlzWse3: 50% - Đang tạo embeddings cho 1 đoạn
2025-05-21 15:18:13 - services.document_chunks_service - INFO - [document_chunks_service.py:141] - Creating embeddings for 1 chunks with 5 parallel workers
2025-05-21 15:18:13 - services.document_chunks_service - INFO - [document_chunks_service.py:146] - Processing batch 1 (1 chunks)
2025-05-21 15:18:13 - core.vector_store - ERROR - [vector_store.py:89] - Error getting Gemini embedding: 'Model' object has no attribute 'embed_content'
2025-05-21 15:18:13 - core.vector_store - WARNING - [vector_store.py:170] - Gemini embedding failed, falling back to Jina: 'Model' object has no attribute 'embed_content'
2025-05-21 15:18:14 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-21 15:18:14 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-Fv-k06gjpIlzWse3: 100% - Hoàn thành xử lý tập tin với 1/1 đoạn
2025-05-21 15:18:14 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-Fv-k06gjpIlzWse3_53f41a9d-589a-4384-a49d-0705b4eabb2b
2025-05-21 15:22:39 - main - INFO - [main.py:82] - Starting up the application
2025-05-21 15:22:39 - main - INFO - [main.py:86] - Ensured temp directory exists: temp-s3
2025-05-21 15:22:39 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-21 15:22:39 - main - INFO - [main.py:91] - Database initialized successfully
2025-05-21 15:25:46 - main - INFO - [main.py:99] - Shutting down the application
2025-05-21 15:25:46 - main - INFO - [main.py:99] - Shutting down the application
2025-05-21 15:25:50 - main - INFO - [main.py:82] - Starting up the application
2025-05-21 15:25:50 - main - INFO - [main.py:86] - Ensured temp directory exists: temp-s3
2025-05-21 15:25:50 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-21 15:25:50 - main - INFO - [main.py:91] - Database initialized successfully
2025-05-21 15:27:39 - main - INFO - [main.py:82] - Starting up the application
2025-05-21 15:27:39 - main - INFO - [main.py:86] - Ensured temp directory exists: temp-s3
2025-05-21 15:27:39 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-21 15:27:39 - main - INFO - [main.py:91] - Database initialized successfully
2025-05-21 15:27:51 - main - INFO - [main.py:148] - Request to /docs completed in 0.0004s
2025-05-21 15:27:51 - main - INFO - [main.py:148] - Request to /openapi.json completed in 0.0082s
2025-05-21 15:28:58 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-J2a_MVK7hjWR4pJu, 34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano%20Ronaldo
2025-05-21 15:28:58 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-J2a_MVK7hjWR4pJu: 0% - Đang bắt đầu xử lý tập tin
2025-05-21 15:28:58 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-J2a_MVK7hjWR4pJu: 5% - Đang tải tập tin
2025-05-21 15:28:58 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano20Ronaldo
2025-05-21 15:28:58 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-J2a_MVK7hjWR4pJu_e54623e2-580c-469c-ba6c-bed6eee24c35
2025-05-21 15:28:59 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-J2a_MVK7hjWR4pJu_e54623e2-580c-469c-ba6c-bed6eee24c35
2025-05-21 15:28:59 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-J2a_MVK7hjWR4pJu_e54623e2-580c-469c-ba6c-bed6eee24c35
2025-05-21 15:28:59 - main - INFO - [main.py:148] - Request to /api/api/files/process-url completed in 0.6069s
2025-05-21 15:28:59 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-J2a_MVK7hjWR4pJu: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-21 15:28:59 - services.markdown_service - INFO - [markdown_service.py:26] - Converting file to Markdown: temp-s3\temp_file-J2a_MVK7hjWR4pJu_e54623e2-580c-469c-ba6c-bed6eee24c35 (type: )
2025-05-21 15:28:59 - services.markdown_service - WARNING - [markdown_service.py:71] - Unsupported file type: , attempting generic conversion
2025-05-21 15:28:59 - services.markdown_service - INFO - [markdown_service.py:82] - Successfully converted file to Markdown (1369 characters)
2025-05-21 15:28:59 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-J2a_MVK7hjWR4pJu: 30% - Đang chia nhỏ nội dung
2025-05-21 15:28:59 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-21 15:28:59 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 1 well-sized chunks
2025-05-21 15:28:59 - services.document_chunks_service - INFO - [document_chunks_service.py:65] - Split content into 1 chunks for file file-J2a_MVK7hjWR4pJu
2025-05-21 15:28:59 - services.document_chunks_service - INFO - [document_chunks_service.py:92] - Successfully saved 1 chunks for file file-J2a_MVK7hjWR4pJu
2025-05-21 15:28:59 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-J2a_MVK7hjWR4pJu: 50% - Đang tạo embeddings cho 1 đoạn
2025-05-21 15:28:59 - services.document_chunks_service - INFO - [document_chunks_service.py:141] - Creating embeddings for 1 chunks with 5 parallel workers
2025-05-21 15:28:59 - services.document_chunks_service - INFO - [document_chunks_service.py:146] - Processing batch 1 (1 chunks)
2025-05-21 15:29:00 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 768 to 2048 dimensions
2025-05-21 15:29:00 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-J2a_MVK7hjWR4pJu: 100% - Hoàn thành xử lý tập tin với 1/1 đoạn
2025-05-21 15:29:00 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-J2a_MVK7hjWR4pJu_e54623e2-580c-469c-ba6c-bed6eee24c35
2025-05-21 15:32:01 - main - INFO - [main.py:99] - Shutting down the application
2025-05-21 15:32:05 - main - INFO - [main.py:82] - Starting up the application
2025-05-21 15:32:05 - main - INFO - [main.py:86] - Ensured temp directory exists: temp-s3
2025-05-21 15:32:05 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-21 15:32:05 - main - INFO - [main.py:91] - Database initialized successfully
2025-05-21 15:32:15 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-LmCF52yDRkG8_PwZ, 14d25a26-e360-4009-93f5-16ce2cc20c59-T%C3%A0i%20li%E1%BB%87u%20h%C6%B0%E1%BB%9Bng%20d%E1%BA%ABn.pdf
2025-05-21 15:32:15 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-LmCF52yDRkG8_PwZ: 0% - Đang bắt đầu xử lý tập tin
2025-05-21 15:32:15 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-LmCF52yDRkG8_PwZ: 5% - Đang tải tập tin
2025-05-21 15:32:15 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/14d25a26-e360-4009-93f5-16ce2cc20c59-TC3A0i20liE1BB87u20hC6B0E1BB9Bng20dE1BAABn.pdf
2025-05-21 15:32:15 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-LmCF52yDRkG8_PwZ_ffcb68cb-e3c2-42ce-9a18-af4d511e2b75.pdf
2025-05-21 15:32:15 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-LmCF52yDRkG8_PwZ_ffcb68cb-e3c2-42ce-9a18-af4d511e2b75.pdf
2025-05-21 15:32:15 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-LmCF52yDRkG8_PwZ_ffcb68cb-e3c2-42ce-9a18-af4d511e2b75.pdf
2025-05-21 15:32:15 - main - INFO - [main.py:148] - Request to /api/api/files/process-url completed in 0.7778s
2025-05-21 15:32:15 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-LmCF52yDRkG8_PwZ: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-21 15:32:15 - services.markdown_service - INFO - [markdown_service.py:26] - Converting file to Markdown: temp-s3\temp_file-LmCF52yDRkG8_PwZ_ffcb68cb-e3c2-42ce-9a18-af4d511e2b75.pdf (type: .pdf)
2025-05-21 15:32:15 - services.markdown_service - INFO - [markdown_service.py:34] - Converting PDF to Markdown
2025-05-21 15:32:16 - services.markdown_service - INFO - [markdown_service.py:82] - Successfully converted file to Markdown (0 characters)
2025-05-21 15:32:16 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-LmCF52yDRkG8_PwZ: 100% - Lỗi: Không thể chuyển đổi tập tin sang Markdown
2025-05-21 15:32:16 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-LmCF52yDRkG8_PwZ_ffcb68cb-e3c2-42ce-9a18-af4d511e2b75.pdf
2025-05-21 15:36:49 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-CzeF6agOxDhVr6i5, 1747448210402-398a6bbc-7a98-4bee-b8bf-6af2df8e85bb.pdf
2025-05-21 15:36:49 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-CzeF6agOxDhVr6i5: 0% - Đang bắt đầu xử lý tập tin
2025-05-21 15:36:49 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-CzeF6agOxDhVr6i5: 5% - Đang tải tập tin
2025-05-21 15:36:49 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/1747448210402-398a6bbc-7a98-4bee-b8bf-6af2df8e85bb.pdf
2025-05-21 15:36:49 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-CzeF6agOxDhVr6i5_166ea345-9a25-48ff-87e6-269902045d80.pdf
2025-05-21 15:36:49 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-CzeF6agOxDhVr6i5_166ea345-9a25-48ff-87e6-269902045d80.pdf
2025-05-21 15:36:49 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-CzeF6agOxDhVr6i5_166ea345-9a25-48ff-87e6-269902045d80.pdf
2025-05-21 15:36:49 - main - INFO - [main.py:148] - Request to /api/api/files/process-url completed in 0.2284s
2025-05-21 15:36:49 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-CzeF6agOxDhVr6i5: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-21 15:36:49 - services.markdown_service - INFO - [markdown_service.py:26] - Converting file to Markdown: temp-s3\temp_file-CzeF6agOxDhVr6i5_166ea345-9a25-48ff-87e6-269902045d80.pdf (type: .pdf)
2025-05-21 15:36:49 - services.markdown_service - INFO - [markdown_service.py:34] - Converting PDF to Markdown
2025-05-21 15:36:49 - services.markdown_service - INFO - [markdown_service.py:82] - Successfully converted file to Markdown (4319 characters)
2025-05-21 15:36:49 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-CzeF6agOxDhVr6i5: 30% - Đang chia nhỏ nội dung
2025-05-21 15:36:49 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-21 15:36:49 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 3 well-sized chunks
2025-05-21 15:36:49 - services.document_chunks_service - INFO - [document_chunks_service.py:65] - Split content into 3 chunks for file file-CzeF6agOxDhVr6i5
2025-05-21 15:36:49 - services.document_chunks_service - INFO - [document_chunks_service.py:92] - Successfully saved 3 chunks for file file-CzeF6agOxDhVr6i5
2025-05-21 15:36:49 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-CzeF6agOxDhVr6i5: 50% - Đang tạo embeddings cho 3 đoạn
2025-05-21 15:36:49 - services.document_chunks_service - INFO - [document_chunks_service.py:141] - Creating embeddings for 3 chunks with 5 parallel workers
2025-05-21 15:36:49 - services.document_chunks_service - INFO - [document_chunks_service.py:146] - Processing batch 1 (3 chunks)
2025-05-21 15:36:50 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 768 to 2048 dimensions
2025-05-21 15:36:50 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 768 to 2048 dimensions
2025-05-21 15:36:50 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 768 to 2048 dimensions
2025-05-21 15:36:50 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-CzeF6agOxDhVr6i5: 100% - Hoàn thành xử lý tập tin với 3/3 đoạn
2025-05-21 15:36:50 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-CzeF6agOxDhVr6i5_166ea345-9a25-48ff-87e6-269902045d80.pdf
2025-05-21 15:37:56 - main - INFO - [main.py:99] - Shutting down the application
2025-05-21 15:47:47 - main - INFO - [main.py:82] - Starting up the application
2025-05-21 15:47:47 - main - INFO - [main.py:86] - Ensured temp directory exists: temp-s3
2025-05-21 15:47:47 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-21 15:47:47 - main - INFO - [main.py:91] - Database initialized successfully
2025-05-21 15:48:22 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-htE32Qc1dCPEW7k0, e33e2301-7c2c-425d-9a15-c917ddb7b37a-si%C3%AAu%20toa%20kh%E1%BB%95ng%20l%E1%BB%93%20c%C3%A1c%20ch%C3%A1u%20%E1%BA%A1.pdf
2025-05-21 15:48:22 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-htE32Qc1dCPEW7k0: 0% - Đang bắt đầu xử lý tập tin
2025-05-21 15:48:22 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-htE32Qc1dCPEW7k0: 5% - Đang tải tập tin
2025-05-21 15:48:22 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/e33e2301-7c2c-425d-9a15-c917ddb7b37a-siC3AAu20toa20khE1BB95ng20lE1BB9320cC3A1c20chC3A1u20E1BAA1.pdf
2025-05-21 15:48:22 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-htE32Qc1dCPEW7k0_578568fc-c9e4-4ed9-a244-dab32d0a2035.pdf
2025-05-21 15:48:27 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-htE32Qc1dCPEW7k0_578568fc-c9e4-4ed9-a244-dab32d0a2035.pdf
2025-05-21 15:48:27 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-htE32Qc1dCPEW7k0_578568fc-c9e4-4ed9-a244-dab32d0a2035.pdf
2025-05-21 15:48:27 - main - INFO - [main.py:148] - Request to /api/api/files/process-url completed in 5.3138s
2025-05-21 15:48:27 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-htE32Qc1dCPEW7k0: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-21 15:48:27 - services.markdown_service - INFO - [markdown_service.py:26] - Converting file to Markdown: temp-s3\temp_file-htE32Qc1dCPEW7k0_578568fc-c9e4-4ed9-a244-dab32d0a2035.pdf (type: .pdf)
2025-05-21 15:48:27 - services.markdown_service - INFO - [markdown_service.py:34] - Converting PDF to Markdown
2025-05-21 15:48:27 - services.markdown_service - INFO - [markdown_service.py:82] - Successfully converted file to Markdown (620 characters)
2025-05-21 15:48:27 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-htE32Qc1dCPEW7k0: 30% - Đang chia nhỏ nội dung
2025-05-21 15:48:27 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-21 15:48:27 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 1 well-sized chunks
2025-05-21 15:48:27 - services.document_chunks_service - INFO - [document_chunks_service.py:65] - Split content into 1 chunks for file file-htE32Qc1dCPEW7k0
2025-05-21 15:48:27 - services.document_chunks_service - INFO - [document_chunks_service.py:92] - Successfully saved 1 chunks for file file-htE32Qc1dCPEW7k0
2025-05-21 15:48:27 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-htE32Qc1dCPEW7k0: 50% - Đang tạo embeddings cho 1 đoạn
2025-05-21 15:48:27 - services.document_chunks_service - INFO - [document_chunks_service.py:141] - Creating embeddings for 1 chunks with 5 parallel workers
2025-05-21 15:48:27 - services.document_chunks_service - INFO - [document_chunks_service.py:146] - Processing batch 1 (1 chunks)
2025-05-21 15:48:29 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 768 to 2048 dimensions
2025-05-21 15:48:29 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-htE32Qc1dCPEW7k0: 100% - Hoàn thành xử lý tập tin với 1/1 đoạn
2025-05-21 15:48:29 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-htE32Qc1dCPEW7k0_578568fc-c9e4-4ed9-a244-dab32d0a2035.pdf
2025-05-21 15:50:16 - main - INFO - [main.py:99] - Shutting down the application
2025-05-21 17:05:53 - main - INFO - [main.py:82] - Starting up the application
2025-05-21 17:05:53 - main - INFO - [main.py:86] - Ensured temp directory exists: temp-s3
2025-05-21 17:05:57 - core.database - ERROR - [database.py:43] - Error initializing database: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused (0x0000274D/10061)
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused (0x0000274D/10061)
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-21 17:05:57 - main - ERROR - [main.py:93] - Error initializing database: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused (0x0000274D/10061)
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused (0x0000274D/10061)
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-22 20:30:36 - main - INFO - [main.py:82] - Starting up the application
2025-05-22 20:30:36 - main - INFO - [main.py:86] - Ensured temp directory exists: temp-s3
2025-05-22 20:30:36 - core.database - ERROR - [database.py:43] - Error initializing database: (psycopg2.errors.InsufficientPrivilege) permission denied for schema public
LINE 2: CREATE TABLE file (
                     ^

[SQL: 
CREATE TABLE file (
	id VARCHAR(100) NOT NULL, 
	filename VARCHAR(255) NOT NULL, 
	filepath VARCHAR(512) NOT NULL, 
	filesize BIGINT NOT NULL, 
	uploaded_at BIGINT NOT NULL, 
	PRIMARY KEY (id)
)

]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-22 20:30:36 - main - ERROR - [main.py:93] - Error initializing database: (psycopg2.errors.InsufficientPrivilege) permission denied for schema public
LINE 2: CREATE TABLE file (
                     ^

[SQL: 
CREATE TABLE file (
	id VARCHAR(100) NOT NULL, 
	filename VARCHAR(255) NOT NULL, 
	filepath VARCHAR(512) NOT NULL, 
	filesize BIGINT NOT NULL, 
	uploaded_at BIGINT NOT NULL, 
	PRIMARY KEY (id)
)

]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-22 20:37:42 - main - INFO - [main.py:95] - Starting up the application
2025-05-22 20:37:42 - main - INFO - [main.py:99] - Ensured temp directory exists: temp-s3
2025-05-22 20:37:42 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-22 20:37:42 - main - INFO - [main.py:104] - Database initialized successfully
2025-05-22 20:38:07 - main - INFO - [main.py:112] - Shutting down the application
2025-05-22 20:40:17 - main - INFO - [main.py:95] - Starting up the application
2025-05-22 20:40:17 - main - INFO - [main.py:99] - Ensured temp directory exists: temp-s3
2025-05-22 20:40:17 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-22 20:40:17 - main - INFO - [main.py:104] - Database initialized successfully
2025-05-22 20:40:33 - main - INFO - [main.py:162] - Request to /docs completed in 0.0005s
2025-05-22 20:40:33 - main - INFO - [main.py:162] - Request to /openapi.json completed in 0.0125s
2025-05-22 20:41:47 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-lC_s329Ruj79JzZ-, e33e2301-7c2c-425d-9a15-c917ddb7b37a-siêu toa khổng lồ các cháu ạ.pdf
2025-05-22 20:41:47 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-lC_s329Ruj79JzZ-: 0% - Đang bắt đầu xử lý tập tin
2025-05-22 20:41:47 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-lC_s329Ruj79JzZ-: 5% - Đang tải tập tin
2025-05-22 20:41:47 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/e33e2301-7c2c-425d-9a15-c917ddb7b37a-sieu_toa_khong_lo_cac_chau_a.pdf
2025-05-22 20:41:47 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-lC_s329Ruj79JzZ-_e7f4d7a1-47b1-4296-9f8a-865dbc7e2f76.pdf
2025-05-22 20:41:49 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-lC_s329Ruj79JzZ-_e7f4d7a1-47b1-4296-9f8a-865dbc7e2f76.pdf
2025-05-22 20:41:49 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-lC_s329Ruj79JzZ-_e7f4d7a1-47b1-4296-9f8a-865dbc7e2f76.pdf
2025-05-22 20:41:49 - main - INFO - [main.py:162] - Request to /api/api/files/process-url completed in 2.9707s
2025-05-22 20:41:49 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-lC_s329Ruj79JzZ-: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-22 20:41:49 - services.markdown_service - INFO - [markdown_service.py:32] - Converting file to Markdown: temp-s3\temp_file-lC_s329Ruj79JzZ-_e7f4d7a1-47b1-4296-9f8a-865dbc7e2f76.pdf (type: .pdf)
2025-05-22 20:41:50 - services.markdown_service - INFO - [markdown_service.py:41] - Converting PDF to Markdown using OCR
2025-05-22 20:41:50 - services.image_processing_service - INFO - [image_processing_service.py:255] - Processing PDF with OCR: temp-s3\temp_file-lC_s329Ruj79JzZ-_e7f4d7a1-47b1-4296-9f8a-865dbc7e2f76.pdf
2025-05-22 20:41:50 - services.image_processing_service - INFO - [image_processing_service.py:39] - Converting PDF to images: temp-s3\temp_file-lC_s329Ruj79JzZ-_e7f4d7a1-47b1-4296-9f8a-865dbc7e2f76.pdf (DPI: 300)
2025-05-22 20:41:50 - services.image_processing_service - ERROR - [image_processing_service.py:76] - Error converting PDF to images: Unable to get page count. Is poppler installed and in PATH?
2025-05-22 20:41:50 - services.image_processing_service - ERROR - [image_processing_service.py:294] - Error processing PDF with OCR: Unable to get page count. Is poppler installed and in PATH?
2025-05-22 20:41:50 - services.markdown_service - ERROR - [markdown_service.py:127] - Error converting file to Markdown: Unable to get page count. Is poppler installed and in PATH?
2025-05-22 20:41:50 - api.file_endpoints - ERROR - [file_endpoints.py:163] - Error processing file file-lC_s329Ruj79JzZ-: Failed to convert file to Markdown: Unable to get page count. Is poppler installed and in PATH?
2025-05-22 20:41:50 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-lC_s329Ruj79JzZ-: 100% - Lỗi xử lý tập tin
2025-05-22 20:41:50 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-lC_s329Ruj79JzZ-_e7f4d7a1-47b1-4296-9f8a-865dbc7e2f76.pdf
2025-05-22 20:48:41 - main - INFO - [main.py:95] - Starting up the application
2025-05-22 20:48:41 - main - INFO - [main.py:99] - Ensured temp directory exists: temp-s3
2025-05-22 20:48:41 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-22 20:48:41 - main - INFO - [main.py:104] - Database initialized successfully
2025-05-22 20:49:16 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-28mny_OgxFIaIgSI, e33e2301-7c2c-425d-9a15-c917ddb7b37a-siêu toa khổng lồ các cháu ạ.pdf
2025-05-22 20:49:16 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-28mny_OgxFIaIgSI: 0% - Đang bắt đầu xử lý tập tin
2025-05-22 20:49:16 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-28mny_OgxFIaIgSI: 5% - Đang tải tập tin
2025-05-22 20:49:16 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/e33e2301-7c2c-425d-9a15-c917ddb7b37a-sieu_toa_khong_lo_cac_chau_a.pdf
2025-05-22 20:49:16 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-28mny_OgxFIaIgSI_e79e2bd4-550c-4aa7-bd25-6d908f8a1188.pdf
2025-05-22 20:49:18 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-28mny_OgxFIaIgSI_e79e2bd4-550c-4aa7-bd25-6d908f8a1188.pdf
2025-05-22 20:49:18 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-28mny_OgxFIaIgSI_e79e2bd4-550c-4aa7-bd25-6d908f8a1188.pdf
2025-05-22 20:49:18 - main - INFO - [main.py:162] - Request to /api/api/files/process-url completed in 3.2281s
2025-05-22 20:49:18 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-28mny_OgxFIaIgSI: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-22 20:49:18 - services.markdown_service - INFO - [markdown_service.py:32] - Converting file to Markdown: temp-s3\temp_file-28mny_OgxFIaIgSI_e79e2bd4-550c-4aa7-bd25-6d908f8a1188.pdf (type: .pdf)
2025-05-22 20:49:18 - services.markdown_service - INFO - [markdown_service.py:41] - Converting PDF to Markdown using OCR
2025-05-22 20:49:18 - services.image_processing_service - INFO - [image_processing_service.py:255] - Processing PDF with OCR: temp-s3\temp_file-28mny_OgxFIaIgSI_e79e2bd4-550c-4aa7-bd25-6d908f8a1188.pdf
2025-05-22 20:49:18 - services.image_processing_service - INFO - [image_processing_service.py:39] - Converting PDF to images: temp-s3\temp_file-28mny_OgxFIaIgSI_e79e2bd4-550c-4aa7-bd25-6d908f8a1188.pdf (DPI: 300)
2025-05-22 20:51:38 - services.image_processing_service - INFO - [image_processing_service.py:72] - Successfully converted PDF to 27 images
2025-05-22 20:51:38 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpg8nvwb4w\page_1.png
2025-05-22 20:51:38 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 20:51:38 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 20:51:38 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 20:51:38 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpg8nvwb4w\page_1.png
2025-05-22 20:51:38 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpg8nvwb4w\page_1.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 20:51:38 - services.image_processing_service - ERROR - [image_processing_service.py:181] - Error performing OCR: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-05-22 20:51:38 - services.image_processing_service - ERROR - [image_processing_service.py:294] - Error processing PDF with OCR: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-05-22 20:51:38 - services.markdown_service - ERROR - [markdown_service.py:127] - Error converting file to Markdown: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-05-22 20:51:38 - api.file_endpoints - ERROR - [file_endpoints.py:163] - Error processing file file-28mny_OgxFIaIgSI: Failed to convert file to Markdown: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-05-22 20:51:38 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-28mny_OgxFIaIgSI: 100% - Lỗi xử lý tập tin
2025-05-22 20:51:38 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-28mny_OgxFIaIgSI_e79e2bd4-550c-4aa7-bd25-6d908f8a1188.pdf
2025-05-22 20:54:28 - main - INFO - [main.py:112] - Shutting down the application
2025-05-22 21:02:38 - main - INFO - [main.py:95] - Starting up the application
2025-05-22 21:02:38 - main - INFO - [main.py:99] - Ensured temp directory exists: temp-s3
2025-05-22 21:02:38 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-22 21:02:38 - main - INFO - [main.py:104] - Database initialized successfully
2025-05-22 21:03:15 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-spfXtqps-mC8ctGt, e33e2301-7c2c-425d-9a15-c917ddb7b37a-siêu toa khổng lồ các cháu ạ.pdf
2025-05-22 21:03:15 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-spfXtqps-mC8ctGt: 0% - Đang bắt đầu xử lý tập tin
2025-05-22 21:03:15 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-spfXtqps-mC8ctGt: 5% - Đang tải tập tin
2025-05-22 21:03:15 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/e33e2301-7c2c-425d-9a15-c917ddb7b37a-sieu_toa_khong_lo_cac_chau_a.pdf
2025-05-22 21:03:15 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-spfXtqps-mC8ctGt_5e376e29-753e-4761-bf43-b03a67aa25c7.pdf
2025-05-22 21:03:17 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-spfXtqps-mC8ctGt_5e376e29-753e-4761-bf43-b03a67aa25c7.pdf
2025-05-22 21:03:17 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-spfXtqps-mC8ctGt_5e376e29-753e-4761-bf43-b03a67aa25c7.pdf
2025-05-22 21:03:17 - main - INFO - [main.py:162] - Request to /api/api/files/process-url completed in 3.0093s
2025-05-22 21:03:17 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-spfXtqps-mC8ctGt: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-22 21:03:17 - services.markdown_service - INFO - [markdown_service.py:32] - Converting file to Markdown: temp-s3\temp_file-spfXtqps-mC8ctGt_5e376e29-753e-4761-bf43-b03a67aa25c7.pdf (type: .pdf)
2025-05-22 21:03:17 - services.markdown_service - INFO - [markdown_service.py:41] - Converting PDF to Markdown using OCR
2025-05-22 21:03:17 - services.image_processing_service - INFO - [image_processing_service.py:255] - Processing PDF with OCR: temp-s3\temp_file-spfXtqps-mC8ctGt_5e376e29-753e-4761-bf43-b03a67aa25c7.pdf
2025-05-22 21:03:17 - services.image_processing_service - INFO - [image_processing_service.py:39] - Converting PDF to images: temp-s3\temp_file-spfXtqps-mC8ctGt_5e376e29-753e-4761-bf43-b03a67aa25c7.pdf (DPI: 300)
2025-05-22 21:05:11 - services.image_processing_service - INFO - [image_processing_service.py:72] - Successfully converted PDF to 27 images
2025-05-22 21:05:11 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_1.png
2025-05-22 21:05:11 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:11 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:11 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:11 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_1.png
2025-05-22 21:05:11 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_1.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:12 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1016 characters from image
2025-05-22 21:05:12 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_2.png
2025-05-22 21:05:12 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:12 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:12 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:12 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_2.png
2025-05-22 21:05:12 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_2.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:13 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1412 characters from image
2025-05-22 21:05:13 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_3.png
2025-05-22 21:05:13 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:14 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:14 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:14 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_3.png
2025-05-22 21:05:14 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_3.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:15 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1178 characters from image
2025-05-22 21:05:15 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_4.png
2025-05-22 21:05:15 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:15 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:15 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:15 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_4.png
2025-05-22 21:05:15 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_4.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:16 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1208 characters from image
2025-05-22 21:05:16 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_5.png
2025-05-22 21:05:16 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:16 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:16 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:16 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_5.png
2025-05-22 21:05:16 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_5.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:17 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 793 characters from image
2025-05-22 21:05:17 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_6.png
2025-05-22 21:05:17 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:17 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:17 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:17 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_6.png
2025-05-22 21:05:17 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_6.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:18 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1136 characters from image
2025-05-22 21:05:18 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_7.png
2025-05-22 21:05:18 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:19 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:19 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:19 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_7.png
2025-05-22 21:05:19 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_7.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:20 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1295 characters from image
2025-05-22 21:05:20 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_8.png
2025-05-22 21:05:20 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:20 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:20 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:20 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_8.png
2025-05-22 21:05:20 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_8.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:21 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1199 characters from image
2025-05-22 21:05:21 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_9.png
2025-05-22 21:05:21 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:21 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:21 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:21 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_9.png
2025-05-22 21:05:21 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_9.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:22 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1061 characters from image
2025-05-22 21:05:22 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_10.png
2025-05-22 21:05:22 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:22 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:22 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:22 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_10.png
2025-05-22 21:05:22 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_10.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:23 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 922 characters from image
2025-05-22 21:05:23 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_11.png
2025-05-22 21:05:23 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:23 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:23 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:23 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_11.png
2025-05-22 21:05:23 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_11.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:24 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1021 characters from image
2025-05-22 21:05:24 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_12.png
2025-05-22 21:05:25 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:25 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:25 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:25 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_12.png
2025-05-22 21:05:25 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_12.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:26 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1180 characters from image
2025-05-22 21:05:26 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_13.png
2025-05-22 21:05:26 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:26 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:26 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:26 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_13.png
2025-05-22 21:05:26 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_13.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:27 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1091 characters from image
2025-05-22 21:05:27 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_14.png
2025-05-22 21:05:27 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:27 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:27 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:27 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_14.png
2025-05-22 21:05:27 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_14.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:29 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1351 characters from image
2025-05-22 21:05:29 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_15.png
2025-05-22 21:05:29 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:29 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:29 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:29 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_15.png
2025-05-22 21:05:29 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_15.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:30 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1403 characters from image
2025-05-22 21:05:30 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_16.png
2025-05-22 21:05:30 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:30 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:30 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:30 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_16.png
2025-05-22 21:05:30 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_16.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:31 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1135 characters from image
2025-05-22 21:05:31 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_17.png
2025-05-22 21:05:31 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:31 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:31 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:31 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_17.png
2025-05-22 21:05:31 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_17.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:32 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1430 characters from image
2025-05-22 21:05:32 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_18.png
2025-05-22 21:05:33 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:33 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:33 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:33 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_18.png
2025-05-22 21:05:33 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_18.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:34 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 2183 characters from image
2025-05-22 21:05:34 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_19.png
2025-05-22 21:05:34 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:34 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:34 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:34 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_19.png
2025-05-22 21:05:34 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_19.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:35 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1580 characters from image
2025-05-22 21:05:35 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_20.png
2025-05-22 21:05:35 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:36 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:36 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:36 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_20.png
2025-05-22 21:05:36 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_20.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:37 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 2011 characters from image
2025-05-22 21:05:37 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_21.png
2025-05-22 21:05:37 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:37 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:37 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:37 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_21.png
2025-05-22 21:05:37 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_21.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:38 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1648 characters from image
2025-05-22 21:05:38 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_22.png
2025-05-22 21:05:38 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:38 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:38 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:38 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_22.png
2025-05-22 21:05:38 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_22.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:40 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 2012 characters from image
2025-05-22 21:05:40 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_23.png
2025-05-22 21:05:40 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:40 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:40 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:40 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_23.png
2025-05-22 21:05:40 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_23.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:41 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1679 characters from image
2025-05-22 21:05:41 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_24.png
2025-05-22 21:05:41 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:41 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:41 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:41 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_24.png
2025-05-22 21:05:41 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_24.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:42 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1692 characters from image
2025-05-22 21:05:42 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_25.png
2025-05-22 21:05:43 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:43 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:43 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:43 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_25.png
2025-05-22 21:05:43 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_25.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:44 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1599 characters from image
2025-05-22 21:05:44 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_26.png
2025-05-22 21:05:44 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:44 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:44 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:44 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_26.png
2025-05-22 21:05:44 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_26.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:45 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 2106 characters from image
2025-05-22 21:05:45 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_27.png
2025-05-22 21:05:46 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-22 21:05:46 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-22 21:05:46 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-22 21:05:46 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_27.png
2025-05-22 21:05:46 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpu7h06_1n\page_27.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-22 21:05:46 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 961 characters from image
2025-05-22 21:05:46 - services.image_processing_service - INFO - [image_processing_service.py:290] - Successfully extracted and cleaned text from 27 pages
2025-05-22 21:05:46 - services.markdown_service - INFO - [markdown_service.py:123] - Successfully converted file to Markdown (37551 characters)
2025-05-22 21:05:46 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-spfXtqps-mC8ctGt: 30% - Đang chia nhỏ nội dung
2025-05-22 21:05:46 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-22 21:05:46 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 28 well-sized chunks
2025-05-22 21:05:46 - services.document_chunks_service - INFO - [document_chunks_service.py:65] - Split content into 28 chunks for file file-spfXtqps-mC8ctGt
2025-05-22 21:05:46 - services.document_chunks_service - INFO - [document_chunks_service.py:92] - Successfully saved 28 chunks for file file-spfXtqps-mC8ctGt
2025-05-22 21:05:46 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-spfXtqps-mC8ctGt: 50% - Đang tạo embeddings cho 28 đoạn
2025-05-22 21:05:46 - services.document_chunks_service - INFO - [document_chunks_service.py:141] - Creating embeddings for 28 chunks with 5 parallel workers
2025-05-22 21:05:46 - services.document_chunks_service - INFO - [document_chunks_service.py:146] - Processing batch 1 (10 chunks)
2025-05-22 21:05:50 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-22 21:05:50 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-22 21:05:50 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-22 21:05:51 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-22 21:05:51 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-22 21:05:51 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:51 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:51 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:51 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:51 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-22 21:05:51 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:51 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:52 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:52 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:52 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:05:52 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:05:53 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:05:53 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:05:54 - services.document_chunks_service - INFO - [document_chunks_service.py:146] - Processing batch 2 (10 chunks)
2025-05-22 21:05:54 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:54 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:55 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:05:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:05:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:05:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:05:56 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:56 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:05:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:05:57 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:57 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:57 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:57 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:57 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:57 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:57 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:05:58 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:05:58 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:05:58 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:05:58 - services.document_chunks_service - INFO - [document_chunks_service.py:146] - Processing batch 3 (8 chunks)
2025-05-22 21:05:59 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:05:59 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:06:00 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:06:00 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:06:00 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:06:00 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:06:00 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:06:00 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:06:00 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:06:00 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:06:00 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:06:00 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:06:00 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:06:00 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:06:01 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:06:01 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:06:01 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:06:01 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:06:01 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:06:01 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:06:01 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-22 21:06:02 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:06:02 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:06:03 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-22 21:06:03 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-spfXtqps-mC8ctGt: 100% - Hoàn thành xử lý tập tin với 28/28 đoạn
2025-05-22 21:06:03 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-spfXtqps-mC8ctGt_5e376e29-753e-4761-bf43-b03a67aa25c7.pdf
2025-05-22 21:07:30 - main - INFO - [main.py:112] - Shutting down the application
2025-05-22 21:07:37 - main - INFO - [main.py:95] - Starting up the application
2025-05-22 21:07:37 - main - INFO - [main.py:99] - Ensured temp directory exists: temp-s3
2025-05-22 21:07:37 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-22 21:07:37 - main - INFO - [main.py:104] - Database initialized successfully
2025-05-23 08:51:13 - main - INFO - [main.py:95] - Starting up the application
2025-05-23 08:51:13 - main - INFO - [main.py:99] - Ensured temp directory exists: temp-s3
2025-05-23 08:51:13 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-23 08:51:13 - main - INFO - [main.py:104] - Database initialized successfully
2025-05-23 08:51:29 - main - INFO - [main.py:162] - Request to /docs completed in 0.0004s
2025-05-23 08:51:29 - main - INFO - [main.py:162] - Request to /openapi.json completed in 0.0071s
2025-05-23 08:52:06 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-L9RfRGy0X1S5PCL1, e33e2301-7c2c-425d-9a15-c917ddb7b37a-siêu toa khổng lồ các cháu ạ.pdf
2025-05-23 08:52:06 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-L9RfRGy0X1S5PCL1: 0% - Đang bắt đầu xử lý tập tin
2025-05-23 08:52:06 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-L9RfRGy0X1S5PCL1: 5% - Đang tải tập tin
2025-05-23 08:52:06 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/e33e2301-7c2c-425d-9a15-c917ddb7b37a-sieu_toa_khong_lo_cac_chau_a.pdf
2025-05-23 08:52:06 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-L9RfRGy0X1S5PCL1_0007d357-e97d-40e8-a8a1-68db9e7181d3.pdf
2025-05-23 08:52:07 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-L9RfRGy0X1S5PCL1_0007d357-e97d-40e8-a8a1-68db9e7181d3.pdf
2025-05-23 08:52:07 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-L9RfRGy0X1S5PCL1_0007d357-e97d-40e8-a8a1-68db9e7181d3.pdf
2025-05-23 08:52:07 - main - INFO - [main.py:162] - Request to /api/api/files/process-url completed in 1.9162s
2025-05-23 08:52:07 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-L9RfRGy0X1S5PCL1: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-23 08:52:07 - services.markdown_service - INFO - [markdown_service.py:32] - Converting file to Markdown: temp-s3\temp_file-L9RfRGy0X1S5PCL1_0007d357-e97d-40e8-a8a1-68db9e7181d3.pdf (type: .pdf)
2025-05-23 08:52:07 - services.markdown_service - INFO - [markdown_service.py:41] - Converting PDF to Markdown using OCR
2025-05-23 08:52:07 - services.image_processing_service - INFO - [image_processing_service.py:255] - Processing PDF with OCR: temp-s3\temp_file-L9RfRGy0X1S5PCL1_0007d357-e97d-40e8-a8a1-68db9e7181d3.pdf
2025-05-23 08:52:07 - services.image_processing_service - INFO - [image_processing_service.py:39] - Converting PDF to images: temp-s3\temp_file-L9RfRGy0X1S5PCL1_0007d357-e97d-40e8-a8a1-68db9e7181d3.pdf (DPI: 300)
2025-05-23 08:53:31 - services.image_processing_service - INFO - [image_processing_service.py:72] - Successfully converted PDF to 27 images
2025-05-23 08:53:31 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_1.png
2025-05-23 08:53:31 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:31 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:31 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:31 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_1.png
2025-05-23 08:53:31 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_1.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:32 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1016 characters from image
2025-05-23 08:53:32 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_2.png
2025-05-23 08:53:32 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:32 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:32 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:32 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_2.png
2025-05-23 08:53:32 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_2.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:33 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1412 characters from image
2025-05-23 08:53:33 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_3.png
2025-05-23 08:53:33 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:33 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:33 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:33 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_3.png
2025-05-23 08:53:33 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_3.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:34 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1178 characters from image
2025-05-23 08:53:34 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_4.png
2025-05-23 08:53:34 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:34 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:34 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:34 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_4.png
2025-05-23 08:53:34 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_4.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:35 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1208 characters from image
2025-05-23 08:53:35 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_5.png
2025-05-23 08:53:35 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:35 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:35 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:35 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_5.png
2025-05-23 08:53:35 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_5.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:36 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 793 characters from image
2025-05-23 08:53:36 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_6.png
2025-05-23 08:53:36 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:36 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:36 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:36 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_6.png
2025-05-23 08:53:36 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_6.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:37 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1136 characters from image
2025-05-23 08:53:37 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_7.png
2025-05-23 08:53:37 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:37 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:37 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:37 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_7.png
2025-05-23 08:53:37 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_7.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:37 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1295 characters from image
2025-05-23 08:53:37 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_8.png
2025-05-23 08:53:38 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:38 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:38 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:38 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_8.png
2025-05-23 08:53:38 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_8.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:38 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1199 characters from image
2025-05-23 08:53:38 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_9.png
2025-05-23 08:53:38 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:39 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:39 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:39 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_9.png
2025-05-23 08:53:39 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_9.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:39 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1061 characters from image
2025-05-23 08:53:39 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_10.png
2025-05-23 08:53:39 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:39 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:39 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:39 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_10.png
2025-05-23 08:53:39 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_10.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:40 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 922 characters from image
2025-05-23 08:53:40 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_11.png
2025-05-23 08:53:40 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:40 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:40 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:40 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_11.png
2025-05-23 08:53:40 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_11.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:41 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1021 characters from image
2025-05-23 08:53:41 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_12.png
2025-05-23 08:53:41 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:41 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:41 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:41 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_12.png
2025-05-23 08:53:41 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_12.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:42 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1180 characters from image
2025-05-23 08:53:42 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_13.png
2025-05-23 08:53:42 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:42 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:42 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:42 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_13.png
2025-05-23 08:53:42 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_13.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:43 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1091 characters from image
2025-05-23 08:53:43 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_14.png
2025-05-23 08:53:43 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:43 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:43 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:43 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_14.png
2025-05-23 08:53:43 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_14.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:44 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1351 characters from image
2025-05-23 08:53:44 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_15.png
2025-05-23 08:53:44 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:44 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:44 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:44 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_15.png
2025-05-23 08:53:44 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_15.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:45 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1403 characters from image
2025-05-23 08:53:45 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_16.png
2025-05-23 08:53:45 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:45 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:45 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:45 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_16.png
2025-05-23 08:53:45 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_16.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:46 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1135 characters from image
2025-05-23 08:53:46 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_17.png
2025-05-23 08:53:46 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:46 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:46 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:46 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_17.png
2025-05-23 08:53:46 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_17.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:47 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1430 characters from image
2025-05-23 08:53:47 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_18.png
2025-05-23 08:53:47 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:47 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:47 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:47 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_18.png
2025-05-23 08:53:47 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_18.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:48 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 2183 characters from image
2025-05-23 08:53:48 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_19.png
2025-05-23 08:53:48 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:48 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:48 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:48 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_19.png
2025-05-23 08:53:48 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_19.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:49 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1580 characters from image
2025-05-23 08:53:49 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_20.png
2025-05-23 08:53:49 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:49 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:49 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:49 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_20.png
2025-05-23 08:53:49 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_20.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:50 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 2011 characters from image
2025-05-23 08:53:50 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_21.png
2025-05-23 08:53:50 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:50 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:50 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:50 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_21.png
2025-05-23 08:53:50 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_21.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:51 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1648 characters from image
2025-05-23 08:53:51 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_22.png
2025-05-23 08:53:51 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:51 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:51 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:51 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_22.png
2025-05-23 08:53:51 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_22.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:52 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 2012 characters from image
2025-05-23 08:53:52 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_23.png
2025-05-23 08:53:52 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:52 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:52 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:52 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_23.png
2025-05-23 08:53:52 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_23.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:53 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1679 characters from image
2025-05-23 08:53:53 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_24.png
2025-05-23 08:53:53 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:53 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:53 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:53 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_24.png
2025-05-23 08:53:53 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_24.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:54 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1692 characters from image
2025-05-23 08:53:54 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_25.png
2025-05-23 08:53:54 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:54 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:54 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:54 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_25.png
2025-05-23 08:53:54 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_25.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:55 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 1599 characters from image
2025-05-23 08:53:55 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_26.png
2025-05-23 08:53:55 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:55 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:55 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:55 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_26.png
2025-05-23 08:53:55 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_26.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:56 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 2106 characters from image
2025-05-23 08:53:56 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_27.png
2025-05-23 08:53:56 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 08:53:56 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 08:53:56 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 08:53:57 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_27.png
2025-05-23 08:53:57 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmp6hxdikm8\page_27.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 08:53:57 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 961 characters from image
2025-05-23 08:53:57 - services.image_processing_service - INFO - [image_processing_service.py:290] - Successfully extracted and cleaned text from 27 pages
2025-05-23 08:53:57 - services.markdown_service - INFO - [markdown_service.py:123] - Successfully converted file to Markdown (37551 characters)
2025-05-23 08:53:57 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-L9RfRGy0X1S5PCL1: 30% - Đang chia nhỏ nội dung
2025-05-23 08:53:57 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-23 08:53:57 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 28 well-sized chunks
2025-05-23 08:53:57 - services.document_chunks_service - INFO - [document_chunks_service.py:65] - Split content into 28 chunks for file file-L9RfRGy0X1S5PCL1
2025-05-23 08:53:57 - services.document_chunks_service - INFO - [document_chunks_service.py:92] - Successfully saved 28 chunks for file file-L9RfRGy0X1S5PCL1
2025-05-23 08:53:57 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-L9RfRGy0X1S5PCL1: 50% - Đang tạo embeddings cho 28 đoạn
2025-05-23 08:53:57 - services.document_chunks_service - INFO - [document_chunks_service.py:141] - Creating embeddings for 28 chunks with 5 parallel workers
2025-05-23 08:53:57 - services.document_chunks_service - INFO - [document_chunks_service.py:146] - Processing batch 1 (10 chunks)
2025-05-23 08:54:00 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-23 08:54:00 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-23 08:54:00 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-23 08:54:00 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-23 08:54:00 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-23 08:54:00 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-23 08:54:01 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:01 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:01 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:01 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:01 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:01 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:01 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:01 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:02 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:02 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:02 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:03 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:03 - services.document_chunks_service - INFO - [document_chunks_service.py:146] - Processing batch 2 (10 chunks)
2025-05-23 08:54:04 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:04 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:05 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:05 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:05 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:05 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:05 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:05 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:05 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:05 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:05 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:05 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:05 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:06 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:06 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:06 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:06 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:06 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:07 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:07 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:07 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:07 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:07 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:07 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:07 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:07 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:07 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:08 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:09 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:09 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:10 - services.document_chunks_service - INFO - [document_chunks_service.py:146] - Processing batch 3 (8 chunks)
2025-05-23 08:54:10 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:10 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:11 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:11 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:11 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:11 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:11 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:11 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:11 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:11 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:11 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:12 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:12 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:12 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:12 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:12 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:12 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:12 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:12 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:13 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:13 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:13 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-23 08:54:14 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:14 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-23 08:54:15 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-L9RfRGy0X1S5PCL1: 100% - Hoàn thành xử lý tập tin với 28/28 đoạn
2025-05-23 08:54:15 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-L9RfRGy0X1S5PCL1_0007d357-e97d-40e8-a8a1-68db9e7181d3.pdf
2025-05-23 08:55:39 - main - INFO - [main.py:112] - Shutting down the application
2025-05-23 09:02:31 - main - INFO - [main.py:96] - Starting up the application
2025-05-23 09:02:31 - main - INFO - [main.py:100] - Ensured temp directory exists: temp-s3
2025-05-23 09:02:31 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-23 09:02:31 - main - INFO - [main.py:105] - Database initialized successfully
2025-05-23 09:02:39 - main - INFO - [main.py:166] - Request to /docs completed in 0.0012s
2025-05-23 09:02:39 - main - INFO - [main.py:166] - Request to /openapi.json completed in 0.0072s
2025-05-23 09:02:45 - api.middleware.api_key_auth - WARNING - [api_key_auth.py:86] - Invalid API key attempt from 127.0.0.1
2025-05-23 09:02:45 - main - INFO - [main.py:166] - Request to /api/api/auth/generate-api-key completed in 0.0005s
2025-05-23 09:03:47 - main - INFO - [main.py:113] - Shutting down the application
2025-05-23 09:03:51 - main - INFO - [main.py:96] - Starting up the application
2025-05-23 09:03:51 - main - INFO - [main.py:100] - Ensured temp directory exists: temp-s3
2025-05-23 09:03:51 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-23 09:03:51 - main - INFO - [main.py:105] - Database initialized successfully
2025-05-23 09:04:05 - api.middleware.api_key_auth - WARNING - [api_key_auth.py:86] - Invalid API key attempt from 127.0.0.1
2025-05-23 09:04:05 - main - INFO - [main.py:166] - Request to /api/api/auth/generate-api-key completed in 0.0007s
2025-05-23 09:04:12 - api.middleware.api_key_auth - WARNING - [api_key_auth.py:86] - Invalid API key attempt from 127.0.0.1
2025-05-23 09:04:12 - main - INFO - [main.py:166] - Request to /api/api/auth/api-key completed in 0.0006s
2025-05-23 09:04:45 - api.auth_endpoints - INFO - [auth_endpoints.py:83] - Generated new API key (demo only)
2025-05-23 09:04:45 - main - INFO - [main.py:166] - Request to /api/api/auth/generate-api-key completed in 0.0184s
2025-05-23 09:04:54 - main - INFO - [main.py:166] - Request to /api/api/auth/api-key completed in 0.0008s
2025-05-23 09:05:37 - api.auth_endpoints - INFO - [auth_endpoints.py:83] - Generated new API key (demo only)
2025-05-23 09:05:37 - main - INFO - [main.py:166] - Request to /api/api/auth/generate-api-key completed in 0.0007s
2025-05-23 09:05:39 - api.auth_endpoints - INFO - [auth_endpoints.py:83] - Generated new API key (demo only)
2025-05-23 09:05:39 - main - INFO - [main.py:166] - Request to /api/api/auth/generate-api-key completed in 0.0015s
2025-05-23 09:05:44 - main - INFO - [main.py:113] - Shutting down the application
2025-05-23 09:05:47 - main - INFO - [main.py:96] - Starting up the application
2025-05-23 09:05:47 - main - INFO - [main.py:100] - Ensured temp directory exists: temp-s3
2025-05-23 09:05:47 - core.database - INFO - [database.py:41] - Database tables created successfully
2025-05-23 09:05:47 - main - INFO - [main.py:105] - Database initialized successfully
2025-05-23 09:10:45 - main - INFO - [main.py:113] - Shutting down the application
2025-05-23 09:10:47 - main - INFO - [main.py:96] - Starting up the application
2025-05-23 09:10:47 - main - INFO - [main.py:100] - Ensured temp directory exists: temp-s3
2025-05-23 09:10:48 - core.database - INFO - [database.py:52] - Created default API key: redai-0648a2d5e3e68074eed740b0be8fb5ca
2025-05-23 09:10:48 - core.database - INFO - [database.py:54] - Database tables created successfully
2025-05-23 09:10:48 - main - INFO - [main.py:105] - Database initialized successfully
2025-05-23 09:13:28 - main - INFO - [main.py:113] - Shutting down the application
2025-05-23 09:13:30 - main - INFO - [main.py:96] - Starting up the application
2025-05-23 09:13:30 - main - INFO - [main.py:100] - Ensured temp directory exists: temp-s3
2025-05-23 09:13:30 - core.database - INFO - [database.py:54] - Database tables created successfully
2025-05-23 09:13:30 - main - INFO - [main.py:105] - Database initialized successfully
2025-05-23 09:13:36 - main - INFO - [main.py:96] - Starting up the application
2025-05-23 09:13:36 - main - INFO - [main.py:100] - Ensured temp directory exists: temp-s3
2025-05-23 09:13:37 - core.database - INFO - [database.py:54] - Database tables created successfully
2025-05-23 09:13:37 - main - INFO - [main.py:105] - Database initialized successfully
2025-05-23 09:13:43 - main - INFO - [main.py:166] - Request to /docs completed in 0.0006s
2025-05-23 09:13:43 - main - INFO - [main.py:166] - Request to /openapi.json completed in 0.0089s
2025-05-23 09:13:52 - api.middleware.api_key_auth - WARNING - [api_key_auth.py:53] - Invalid API key attempt from 127.0.0.1
2025-05-23 09:13:53 - main - INFO - [main.py:166] - Request to /api/auth/keys completed in 0.0093s
2025-05-23 09:14:08 - services.api_key_service - INFO - [api_key_service.py:38] - Created new API key: redai-cfa8...
2025-05-23 09:14:08 - main - INFO - [main.py:166] - Request to /api/auth/keys completed in 0.0315s
2025-05-23 09:14:31 - services.api_key_service - INFO - [api_key_service.py:70] - Retrieved 2 API keys
2025-05-23 09:14:31 - main - INFO - [main.py:166] - Request to /api/auth/keys completed in 0.0119s
2025-05-23 09:18:34 - main - INFO - [main.py:113] - Shutting down the application
2025-05-23 09:18:37 - main - INFO - [main.py:96] - Starting up the application
2025-05-23 09:18:37 - main - INFO - [main.py:100] - Ensured temp directory exists: temp-s3
2025-05-23 09:18:37 - core.database - INFO - [database.py:54] - Database tables created successfully
2025-05-23 09:18:37 - main - INFO - [main.py:105] - Database initialized successfully
2025-05-23 09:19:39 - main - INFO - [main.py:113] - Shutting down the application
2025-05-23 09:21:34 - main - INFO - [main.py:96] - Starting up the application
2025-05-23 09:21:34 - main - INFO - [main.py:100] - Ensured temp directory exists: temp-s3
2025-05-23 09:21:34 - core.database - INFO - [database.py:54] - Database tables created successfully
2025-05-23 09:21:34 - main - INFO - [main.py:105] - Database initialized successfully
2025-05-23 09:21:37 - main - INFO - [main.py:166] - Request to /docs completed in 0.0004s
2025-05-23 09:21:37 - main - INFO - [main.py:166] - Request to /openapi.json completed in 0.0057s
2025-05-23 09:22:12 - services.api_key_service - INFO - [api_key_service.py:38] - Created new API key: redai-aa89...
2025-05-23 09:22:12 - main - INFO - [main.py:166] - Request to /api/auth/keys completed in 0.0417s
2025-05-23 09:31:04 - api.middleware.api_key_auth - WARNING - [api_key_auth.py:97] - Invalid API key attempt from 127.0.0.1
2025-05-23 09:31:04 - main - INFO - [main.py:166] - Request to /api/api/files/process-url completed in 0.0181s
2025-05-23 09:31:20 - api.middleware.api_key_auth - WARNING - [api_key_auth.py:97] - Invalid API key attempt from 127.0.0.1
2025-05-23 09:31:20 - main - INFO - [main.py:166] - Request to /api/api/files/process-url completed in 0.0008s
2025-05-23 09:31:32 - services.api_key_service - INFO - [api_key_service.py:38] - Created new API key: redai-382c...
2025-05-23 09:31:32 - main - INFO - [main.py:166] - Request to /api/auth/keys completed in 0.0647s
2025-05-23 09:31:48 - api.middleware.api_key_auth - WARNING - [api_key_auth.py:97] - Invalid API key attempt from 127.0.0.1
2025-05-23 09:31:48 - main - INFO - [main.py:166] - Request to /api/api/files/process-url completed in 0.0003s
2025-05-23 09:41:34 - main - INFO - [main.py:113] - Shutting down the application
2025-05-23 09:41:38 - main - INFO - [main.py:96] - Starting up the application
2025-05-23 09:41:38 - main - INFO - [main.py:100] - Ensured temp directory exists: temp-s3
2025-05-23 09:41:39 - core.database - INFO - [database.py:54] - Database tables created successfully
2025-05-23 09:41:39 - main - INFO - [main.py:105] - Database initialized successfully
2025-05-23 09:43:09 - main - INFO - [main.py:96] - Starting up the application
2025-05-23 09:43:09 - main - INFO - [main.py:100] - Ensured temp directory exists: temp-s3
2025-05-23 09:43:09 - core.database - INFO - [database.py:54] - Database tables created successfully
2025-05-23 09:43:09 - main - INFO - [main.py:105] - Database initialized successfully
2025-05-23 09:45:50 - main - INFO - [main.py:166] - Request to /docs completed in 0.0034s
2025-05-23 09:45:51 - main - INFO - [main.py:166] - Request to /openapi.json completed in 0.0353s
2025-05-23 09:45:59 - services.api_key_service - INFO - [api_key_service.py:38] - Created new API key: redai-01d8...
2025-05-23 09:45:59 - main - INFO - [main.py:166] - Request to /api/auth/keys completed in 0.1196s
2025-05-23 09:46:21 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: None...
2025-05-23 09:46:21 - api.middleware.api_key_auth - WARNING - [api_key_auth.py:112] - Missing API key in request from 127.0.0.1
2025-05-23 09:46:21 - main - INFO - [main.py:166] - Request to /api/files/process-url completed in 0.0014s
2025-05-23 09:51:51 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 09:51:51 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 09:51:51 - core.database - INFO - [database.py:54] - Database tables created successfully
2025-05-23 09:51:51 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 09:51:55 - services.api_key_service - INFO - [api_key_service.py:38] - Created new API key: redai-07fd...
2025-05-23 09:51:55 - main - INFO - [main.py:176] - Request to /api/auth/keys completed in 0.0221s
2025-05-23 09:57:57 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-07fd...
2025-05-23 09:57:57 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-23 09:57:57 - services.s3_file_service - WARNING - [s3_file_service.py:58] - URL not accessible: https://redaivn.hn.ss.bfcplatform.vn/knowledge_files/4/34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano%20Ronaldo?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=SB4NHSLW6ZL16S3NNFZQ%2F20250523%2Fauto%2Fs3%2Faws4_request&X-Amz-Date=20250523T025757Z&X-Amz-Expires=86400&X-Amz-Signature=09db0a284b5b98556691710fb3fcb3f5b7da78f597347da1bfcbe0c0ff57b719&X-Amz-SignedHeaders=host&x-amz-checksum-mode=ENABLED&x-id=GetObject, status code: 403
2025-05-23 09:57:57 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.3411s
2025-05-23 10:06:57 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 10:07:02 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 10:07:02 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 10:07:02 - core.database - INFO - [database.py:54] - Database tables created successfully
2025-05-23 10:07:02 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 10:15:16 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 10:15:16 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 10:15:17 - core.database - INFO - [database.py:54] - Database tables created successfully
2025-05-23 10:15:17 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 10:15:20 - main - INFO - [main.py:176] - Request to /docs completed in 0.0008s
2025-05-23 10:15:21 - main - INFO - [main.py:176] - Request to /openapi.json completed in 0.0098s
2025-05-23 10:19:31 - services.api_key_service - INFO - [api_key_service.py:38] - Created new API key: redai-eb31...
2025-05-23 10:19:31 - main - INFO - [main.py:176] - Request to /api/auth/keys completed in 0.0587s
2025-05-23 10:19:35 - services.api_key_service - INFO - [api_key_service.py:38] - Created new API key: redai-d6b0...
2025-05-23 10:19:35 - main - INFO - [main.py:176] - Request to /api/auth/keys completed in 0.0134s
2025-05-23 10:20:04 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-d6b0...
2025-05-23 10:20:04 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-d6b0... (length: 42)
2025-05-23 10:20:04 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-biw0BfrgnGn5yjPL, 1746691711706-4c9d223f-9fb3-4366-8137-514e91f20a02.pdf
2025-05-23 10:20:04 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-biw0BfrgnGn5yjPL: 0% - Đang bắt đầu xử lý tập tin
2025-05-23 10:20:04 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-biw0BfrgnGn5yjPL: 5% - Đang tải tập tin
2025-05-23 10:20:04 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/1746691711706-4c9d223f-9fb3-4366-8137-514e91f20a02.pdf
2025-05-23 10:20:04 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-biw0BfrgnGn5yjPL_11254b8f-5c5c-4171-9b12-4421b35d0544.pdf
2025-05-23 10:20:04 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-biw0BfrgnGn5yjPL_11254b8f-5c5c-4171-9b12-4421b35d0544.pdf
2025-05-23 10:20:04 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-biw0BfrgnGn5yjPL_11254b8f-5c5c-4171-9b12-4421b35d0544.pdf
2025-05-23 10:20:04 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.5627s
2025-05-23 10:20:04 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-biw0BfrgnGn5yjPL: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-23 10:20:04 - services.markdown_service - INFO - [markdown_service.py:32] - Converting file to Markdown: temp-s3\temp_file-biw0BfrgnGn5yjPL_11254b8f-5c5c-4171-9b12-4421b35d0544.pdf (type: .pdf)
2025-05-23 10:20:04 - services.markdown_service - INFO - [markdown_service.py:41] - Converting PDF to Markdown using OCR
2025-05-23 10:20:04 - services.image_processing_service - INFO - [image_processing_service.py:255] - Processing PDF with OCR: temp-s3\temp_file-biw0BfrgnGn5yjPL_11254b8f-5c5c-4171-9b12-4421b35d0544.pdf
2025-05-23 10:20:04 - services.image_processing_service - INFO - [image_processing_service.py:39] - Converting PDF to images: temp-s3\temp_file-biw0BfrgnGn5yjPL_11254b8f-5c5c-4171-9b12-4421b35d0544.pdf (DPI: 300)
2025-05-23 10:20:05 - services.image_processing_service - INFO - [image_processing_service.py:72] - Successfully converted PDF to 1 images
2025-05-23 10:20:05 - services.image_processing_service - INFO - [image_processing_service.py:109] - Processing image: C:\Users\<USER>\AppData\Local\Temp\tmpb05831z0\page_1.png
2025-05-23 10:20:05 - services.image_processing_service - INFO - [image_processing_service.py:117] - Applied grayscale conversion
2025-05-23 10:20:06 - services.image_processing_service - INFO - [image_processing_service.py:123] - Applied sharpening
2025-05-23 10:20:06 - services.image_processing_service - INFO - [image_processing_service.py:133] - Applied binary thresholding with value 150
2025-05-23 10:20:06 - services.image_processing_service - INFO - [image_processing_service.py:141] - Saved processed image to C:\Users\<USER>\AppData\Local\Temp\tmpb05831z0\page_1.png
2025-05-23 10:20:06 - services.image_processing_service - INFO - [image_processing_service.py:169] - Performing OCR on image: C:\Users\<USER>\AppData\Local\Temp\tmpb05831z0\page_1.png (lang: eng+vie, config: --psm 6 --oem 3)
2025-05-23 10:20:06 - services.image_processing_service - INFO - [image_processing_service.py:177] - Successfully extracted 409 characters from image
2025-05-23 10:20:06 - services.image_processing_service - INFO - [image_processing_service.py:290] - Successfully extracted and cleaned text from 1 pages
2025-05-23 10:20:06 - services.markdown_service - INFO - [markdown_service.py:123] - Successfully converted file to Markdown (439 characters)
2025-05-23 10:20:06 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-biw0BfrgnGn5yjPL: 30% - Đang chia nhỏ nội dung
2025-05-23 10:20:06 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-23 10:20:06 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 1 well-sized chunks
2025-05-23 10:20:06 - services.document_chunks_service - INFO - [document_chunks_service.py:64] - Split content into 1 chunks for file file-biw0BfrgnGn5yjPL
2025-05-23 10:20:06 - services.document_chunks_service - INFO - [document_chunks_service.py:91] - Successfully saved 1 chunks for file file-biw0BfrgnGn5yjPL
2025-05-23 10:20:06 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-biw0BfrgnGn5yjPL: 50% - Đang tạo embeddings cho 1 đoạn
2025-05-23 10:20:06 - services.document_chunks_service - INFO - [document_chunks_service.py:140] - Creating embeddings for 1 chunks with 5 parallel workers
2025-05-23 10:20:06 - services.document_chunks_service - INFO - [document_chunks_service.py:145] - Processing batch 1 (1 chunks)
2025-05-23 10:20:09 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-23 10:20:09 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-biw0BfrgnGn5yjPL: 100% - Hoàn thành xử lý tập tin với 1/1 đoạn
2025-05-23 10:20:09 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-biw0BfrgnGn5yjPL_11254b8f-5c5c-4171-9b12-4421b35d0544.pdf
2025-05-23 10:25:50 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 10:25:58 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 10:25:58 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 10:25:58 - core.database - INFO - [database.py:54] - Database tables created successfully
2025-05-23 10:25:58 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 10:27:03 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-07fd...
2025-05-23 10:27:03 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-23 10:27:03 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-dNRLX8CFJFW9MlEl, 34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano Ronaldo
2025-05-23 10:27:03 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-dNRLX8CFJFW9MlEl: 0% - Đang bắt đầu xử lý tập tin
2025-05-23 10:27:03 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-dNRLX8CFJFW9MlEl: 5% - Đang tải tập tin
2025-05-23 10:27:03 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano_Ronaldo
2025-05-23 10:27:03 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-dNRLX8CFJFW9MlEl_e04c0785-0fdb-4230-a446-ce3e3af63a9f
2025-05-23 10:27:03 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-dNRLX8CFJFW9MlEl_e04c0785-0fdb-4230-a446-ce3e3af63a9f
2025-05-23 10:27:03 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-dNRLX8CFJFW9MlEl_e04c0785-0fdb-4230-a446-ce3e3af63a9f
2025-05-23 10:27:03 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.3502s
2025-05-23 10:27:03 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-dNRLX8CFJFW9MlEl: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-23 10:27:03 - services.markdown_service - INFO - [markdown_service.py:32] - Converting file to Markdown: temp-s3\temp_file-dNRLX8CFJFW9MlEl_e04c0785-0fdb-4230-a446-ce3e3af63a9f (type: )
2025-05-23 10:27:04 - services.markdown_service - WARNING - [markdown_service.py:112] - Unsupported file type: , attempting generic conversion
2025-05-23 10:27:04 - services.markdown_service - INFO - [markdown_service.py:123] - Successfully converted file to Markdown (1369 characters)
2025-05-23 10:27:04 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-dNRLX8CFJFW9MlEl: 30% - Đang chia nhỏ nội dung
2025-05-23 10:27:04 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-23 10:27:04 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 1 well-sized chunks
2025-05-23 10:27:04 - services.document_chunks_service - INFO - [document_chunks_service.py:64] - Split content into 1 chunks for file file-dNRLX8CFJFW9MlEl
2025-05-23 10:27:04 - services.document_chunks_service - INFO - [document_chunks_service.py:91] - Successfully saved 1 chunks for file file-dNRLX8CFJFW9MlEl
2025-05-23 10:27:04 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-dNRLX8CFJFW9MlEl: 50% - Đang tạo embeddings cho 1 đoạn
2025-05-23 10:27:04 - services.document_chunks_service - INFO - [document_chunks_service.py:140] - Creating embeddings for 1 chunks with 5 parallel workers
2025-05-23 10:27:04 - services.document_chunks_service - INFO - [document_chunks_service.py:145] - Processing batch 1 (1 chunks)
2025-05-23 10:27:06 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-23 10:27:06 - api.file_endpoints - INFO - [file_endpoints.py:96] - Progress update for file-dNRLX8CFJFW9MlEl: 100% - Hoàn thành xử lý tập tin với 1/1 đoạn
2025-05-23 10:27:06 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-dNRLX8CFJFW9MlEl_e04c0785-0fdb-4230-a446-ce3e3af63a9f
2025-05-23 10:27:06 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/file-dNRLX8CFJFW9MlEl/progress with API key: redai-07fd...
2025-05-23 10:27:06 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-23 10:27:06 - main - INFO - [main.py:176] - Request to /api/files/file-dNRLX8CFJFW9MlEl/progress completed in 0.0089s
2025-05-23 10:43:19 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/file-dNRLX8CFJFW9MlEl/progress with API key: redai-d6b0...
2025-05-23 10:43:19 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-d6b0... (length: 42)
2025-05-23 10:43:19 - main - INFO - [main.py:176] - Request to /api/files/file-dNRLX8CFJFW9MlEl/progress completed in 0.1172s
2025-05-23 10:43:26 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/file-dNRLX8CFJFW9MlEl/chunks with API key: redai-d6b0...
2025-05-23 10:43:26 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-d6b0... (length: 42)
2025-05-23 10:43:26 - services.document_chunks_service - INFO - [document_chunks_service.py:288] - Retrieved 1 chunks for file file-dNRLX8CFJFW9MlEl
2025-05-23 10:43:26 - main - INFO - [main.py:176] - Request to /api/files/file-dNRLX8CFJFW9MlEl/chunks completed in 0.1956s
2025-05-23 10:44:32 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 11:17:06 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 11:17:06 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 11:17:06 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 11:17:06 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 11:17:11 - main - INFO - [main.py:176] - Request to /docs completed in 0.0005s
2025-05-23 11:17:11 - main - INFO - [main.py:176] - Request to /openapi.json completed in 0.0206s
2025-05-23 11:21:28 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 11:23:15 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 11:23:15 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 11:23:15 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 11:23:15 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 11:23:34 - main - INFO - [main.py:176] - Request to /docs completed in 0.0004s
2025-05-23 11:23:34 - main - INFO - [main.py:176] - Request to /openapi.json completed in 0.0067s
2025-05-23 11:23:48 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/vector-stores with API key: redai-d6b0...
2025-05-23 11:23:48 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-d6b0... (length: 42)
2025-05-23 11:23:48 - services.vector_store_service - ERROR - [vector_store_service.py:49] - Database error creating vector store: (psycopg2.errors.NotNullViolation) null value in column "storage" of relation "vector_stores" violates not-null constraint
DETAIL:  Failing row contains (vs_9eb56d959487915a445fc15cc081a42b, meo-meo-meo, null, null, null, 1747974228481, 1747974228481).

[SQL: INSERT INTO vector_stores (id, name, created_at, update_at) VALUES (%(id)s, %(name)s, %(created_at)s, %(update_at)s)]
[parameters: {'id': 'vs_9eb56d959487915a445fc15cc081a42b', 'name': 'meo-meo-meo', 'created_at': 1747974228481, 'update_at': 1747974228481}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-23 11:23:48 - api.vector_store_endpoints - ERROR - [vector_store_endpoints.py:52] - Error creating vector store: (psycopg2.errors.NotNullViolation) null value in column "storage" of relation "vector_stores" violates not-null constraint
DETAIL:  Failing row contains (vs_9eb56d959487915a445fc15cc081a42b, meo-meo-meo, null, null, null, 1747974228481, 1747974228481).

[SQL: INSERT INTO vector_stores (id, name, created_at, update_at) VALUES (%(id)s, %(name)s, %(created_at)s, %(update_at)s)]
[parameters: {'id': 'vs_9eb56d959487915a445fc15cc081a42b', 'name': 'meo-meo-meo', 'created_at': 1747974228481, 'update_at': 1747974228481}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-23 11:23:48 - main - INFO - [main.py:176] - Request to /api/vector-stores completed in 0.0462s
2025-05-23 11:28:13 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 11:28:15 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 11:28:15 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 11:28:15 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 11:28:15 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 11:28:18 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 11:28:18 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 11:28:18 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 11:28:18 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 11:28:30 - main - INFO - [main.py:176] - Request to /docs completed in 0.0004s
2025-05-23 11:28:30 - main - INFO - [main.py:176] - Request to /openapi.json completed in 0.0069s
2025-05-23 11:28:46 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/vector-stores with API key: redai-d6b0...
2025-05-23 11:28:46 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-d6b0... (length: 42)
2025-05-23 11:28:46 - services.vector_store_service - ERROR - [vector_store_service.py:52] - Database error creating vector store: (psycopg2.errors.NotNullViolation) null value in column "owner_type" of relation "vector_stores" violates not-null constraint
DETAIL:  Failing row contains (vs_2a62d9700c53e3b3baa5351eb372b247, meo-meo-meo, 1024, null, null, 1747974526430, 1747974526430).

[SQL: INSERT INTO vector_stores (id, name, storage, created_at, update_at) VALUES (%(id)s, %(name)s, %(storage)s, %(created_at)s, %(update_at)s)]
[parameters: {'id': 'vs_2a62d9700c53e3b3baa5351eb372b247', 'name': 'meo-meo-meo', 'storage': 1024, 'created_at': 1747974526430, 'update_at': 1747974526430}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-23 11:28:46 - api.vector_store_endpoints - ERROR - [vector_store_endpoints.py:53] - Error creating vector store: (psycopg2.errors.NotNullViolation) null value in column "owner_type" of relation "vector_stores" violates not-null constraint
DETAIL:  Failing row contains (vs_2a62d9700c53e3b3baa5351eb372b247, meo-meo-meo, 1024, null, null, 1747974526430, 1747974526430).

[SQL: INSERT INTO vector_stores (id, name, storage, created_at, update_at) VALUES (%(id)s, %(name)s, %(storage)s, %(created_at)s, %(update_at)s)]
[parameters: {'id': 'vs_2a62d9700c53e3b3baa5351eb372b247', 'name': 'meo-meo-meo', 'storage': 1024, 'created_at': 1747974526430, 'update_at': 1747974526430}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-23 11:28:46 - main - INFO - [main.py:176] - Request to /api/vector-stores completed in 0.0560s
2025-05-23 11:30:10 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/vector-stores with API key: redai-d6b0...
2025-05-23 11:30:10 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-d6b0... (length: 42)
2025-05-23 11:30:10 - services.vector_store_service - INFO - [vector_store_service.py:47] - Created vector store: vs_16f6701ea78f211a0b6142977f6afe55, meo-meo-meo, storage=1024
2025-05-23 11:30:10 - main - INFO - [main.py:176] - Request to /api/vector-stores completed in 0.0391s
2025-05-23 11:30:23 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/vector-stores with API key: redai-d6b0...
2025-05-23 11:30:23 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-d6b0... (length: 42)
2025-05-23 11:30:23 - services.vector_store_service - INFO - [vector_store_service.py:92] - Retrieved 1 vector stores
2025-05-23 11:30:23 - main - INFO - [main.py:176] - Request to /api/vector-stores completed in 0.0218s
2025-05-23 11:30:38 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/vector-stores/vs_16f6701ea78f211a0b6142977f6afe55 with API key: redai-d6b0...
2025-05-23 11:30:38 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-d6b0... (length: 42)
2025-05-23 11:30:38 - services.vector_store_service - INFO - [vector_store_service.py:117] - Retrieved vector store: vs_16f6701ea78f211a0b6142977f6afe55
2025-05-23 11:30:38 - main - INFO - [main.py:176] - Request to /api/vector-stores/vs_16f6701ea78f211a0b6142977f6afe55 completed in 0.0398s
2025-05-23 11:31:01 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/vector-stores/vs_16f6701ea78f211a0b6142977f6afe55 with API key: redai-d6b0...
2025-05-23 11:31:01 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-d6b0... (length: 42)
2025-05-23 11:31:01 - main - INFO - [main.py:176] - Request to /api/vector-stores/vs_16f6701ea78f211a0b6142977f6afe55 completed in 0.0062s
2025-05-23 11:31:08 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/vector-stores/vs_16f6701ea78f211a0b6142977f6afe55 with API key: redai-d6b0...
2025-05-23 11:31:08 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-d6b0... (length: 42)
2025-05-23 11:31:08 - services.vector_store_service - INFO - [vector_store_service.py:173] - Updated vector store: vs_16f6701ea78f211a0b6142977f6afe55
2025-05-23 11:31:08 - main - INFO - [main.py:176] - Request to /api/vector-stores/vs_16f6701ea78f211a0b6142977f6afe55 completed in 0.0776s
2025-05-23 12:00:46 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/vector-stores with API key: redai-07fd...
2025-05-23 12:00:47 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-23 12:00:47 - services.vector_store_service - INFO - [vector_store_service.py:47] - Created vector store: vs_b6bf2bd5b69198c4b3bc946e15c53b51, meo-moe-meoooooooo, storage=1024
2025-05-23 12:00:47 - main - INFO - [main.py:176] - Request to /api/vector-stores completed in 0.4282s
2025-05-23 12:01:12 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/vector-stores with API key: redai-d6b0...
2025-05-23 12:01:12 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-d6b0... (length: 42)
2025-05-23 12:01:13 - services.vector_store_service - INFO - [vector_store_service.py:92] - Retrieved 2 vector stores
2025-05-23 12:01:13 - main - INFO - [main.py:176] - Request to /api/vector-stores completed in 0.0820s
2025-05-23 12:08:41 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/vector-stores with API key: redai-07fd...
2025-05-23 12:08:41 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-23 12:08:41 - services.vector_store_service - INFO - [vector_store_service.py:47] - Created vector store: vs_d70b31f189d2bdb275b33cbaa3d15cc7, meo-moe-meoooooooo, storage=1024
2025-05-23 12:08:41 - main - INFO - [main.py:176] - Request to /api/vector-stores completed in 0.3041s
2025-05-23 12:08:50 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/vector-stores with API key: redai-d6b0...
2025-05-23 12:08:50 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-d6b0... (length: 42)
2025-05-23 12:08:50 - services.vector_store_service - INFO - [vector_store_service.py:92] - Retrieved 1 vector stores
2025-05-23 12:08:50 - main - INFO - [main.py:176] - Request to /api/vector-stores completed in 0.0457s
2025-05-23 12:12:01 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/vector-stores with API key: redai-07fd...
2025-05-23 12:12:01 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-23 12:12:01 - services.vector_store_service - INFO - [vector_store_service.py:47] - Created vector store: vs_1cbb5304ceae85e591e5268198095df0, meo-moe-meoooooooo, storage=0
2025-05-23 12:12:01 - main - INFO - [main.py:176] - Request to /api/vector-stores completed in 0.0945s
2025-05-23 13:43:13 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 13:43:21 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 13:43:21 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 13:43:21 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 13:43:21 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 13:45:15 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 13:45:15 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 13:45:15 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 13:45:15 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 13:45:21 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 13:45:21 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 13:45:21 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 13:45:21 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 13:45:28 - main - INFO - [main.py:176] - Request to /docs completed in 0.0005s
2025-05-23 13:45:28 - main - INFO - [main.py:176] - Request to /openapi.json completed in 0.0181s
2025-05-23 13:47:07 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-d6b0...
2025-05-23 13:47:07 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-d6b0... (length: 42)
2025-05-23 13:47:08 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-IRqDdjNnqc2ndz5j, 34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano Ronaldo
2025-05-23 13:47:08 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-IRqDdjNnqc2ndz5j: 0% - Đang bắt đầu xử lý tập tin
2025-05-23 13:47:08 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-IRqDdjNnqc2ndz5j: 5% - Đang tải tập tin
2025-05-23 13:47:08 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano_Ronaldo
2025-05-23 13:47:08 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-IRqDdjNnqc2ndz5j_2e660f49-962f-46ca-9cee-464259cf9e9c
2025-05-23 13:47:08 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-IRqDdjNnqc2ndz5j_2e660f49-962f-46ca-9cee-464259cf9e9c
2025-05-23 13:47:08 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-IRqDdjNnqc2ndz5j_2e660f49-962f-46ca-9cee-464259cf9e9c
2025-05-23 13:47:08 - api.file_endpoints - INFO - [file_endpoints.py:286] - Using vector store: meo-moe-meoooooooo (vs_1cbb5304ceae85e591e5268198095df0)
2025-05-23 13:47:08 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 1.1468s
2025-05-23 13:47:08 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-IRqDdjNnqc2ndz5j: 5% - Đang xử lý tập tin với kho vector: meo-moe-meoooooooo
2025-05-23 13:47:08 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-IRqDdjNnqc2ndz5j: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-23 13:47:08 - services.markdown_service - INFO - [markdown_service.py:32] - Converting file to Markdown: temp-s3\temp_file-IRqDdjNnqc2ndz5j_2e660f49-962f-46ca-9cee-464259cf9e9c (type: )
2025-05-23 13:47:08 - services.markdown_service - WARNING - [markdown_service.py:112] - Unsupported file type: , attempting generic conversion
2025-05-23 13:47:08 - services.markdown_service - INFO - [markdown_service.py:123] - Successfully converted file to Markdown (1369 characters)
2025-05-23 13:47:08 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-IRqDdjNnqc2ndz5j: 30% - Đang chia nhỏ nội dung
2025-05-23 13:47:08 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-23 13:47:08 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 1 well-sized chunks
2025-05-23 13:47:08 - services.document_chunks_service - INFO - [document_chunks_service.py:66] - Split content into 1 chunks for file file-IRqDdjNnqc2ndz5j
2025-05-23 13:47:08 - services.document_chunks_service - INFO - [document_chunks_service.py:80] - Associating chunks with vector store: vs_1cbb5304ceae85e591e5268198095df0 (meo-moe-meoooooooo)
2025-05-23 13:47:08 - services.document_chunks_service - ERROR - [document_chunks_service.py:109] - Database error creating chunks: (psycopg2.errors.UndefinedColumn) column "vector_store_id" of relation "document_chunks" does not exist
LINE 1: ...d, file_id, chunk_index, content, chunk_metadata, vector_sto...
                                                             ^

[SQL: INSERT INTO document_chunks (id, file_id, chunk_index, content, chunk_metadata, vector_store_id, embedding, embedding_source, embedding_dimensions, created_at, updated_at) VALUES (%(id)s::UUID, %(file_id)s, %(chunk_index)s, %(content)s, %(chunk_metadata)s::JSONB, %(vector_store_id)s, %(embedding)s, %(embedding_source)s, %(embedding_dimensions)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('f2964d38-8b0a-413f-8063-80b0ff92d7cb'), 'file_id': 'file-IRqDdjNnqc2ndz5j', 'chunk_index': 0, 'content': 'TUYỂN DỤNG THỰC TẬP SINH BACKEND\n\nCông ty CP Đầu tư và Thương mại Redon đang tìm kiếm Thực tập sinh Backend tiềm\n\nnăng để cùng đồng hành và phát  ... (1222 characters truncated) ... quan nếu có) về: <EMAIL>\n\nvới tiêu đề ghi rõ: [TTS Frontend] – Họ và Tên\n\n\uf0fc\n\nHoặc liên hệ qua Zalo bằng số điện thoại: 0865475477', 'chunk_metadata': '{}', 'vector_store_id': 'vs_1cbb5304ceae85e591e5268198095df0', 'embedding': None, 'embedding_source': 'gemini', 'embedding_dimensions': 2048, 'created_at': 1747982828953, 'updated_at': 1747982828953}]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-23 13:47:08 - api.file_endpoints - ERROR - [file_endpoints.py:201] - Error processing file file-IRqDdjNnqc2ndz5j: (psycopg2.errors.UndefinedColumn) column "vector_store_id" of relation "document_chunks" does not exist
LINE 1: ...d, file_id, chunk_index, content, chunk_metadata, vector_sto...
                                                             ^

[SQL: INSERT INTO document_chunks (id, file_id, chunk_index, content, chunk_metadata, vector_store_id, embedding, embedding_source, embedding_dimensions, created_at, updated_at) VALUES (%(id)s::UUID, %(file_id)s, %(chunk_index)s, %(content)s, %(chunk_metadata)s::JSONB, %(vector_store_id)s, %(embedding)s, %(embedding_source)s, %(embedding_dimensions)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('f2964d38-8b0a-413f-8063-80b0ff92d7cb'), 'file_id': 'file-IRqDdjNnqc2ndz5j', 'chunk_index': 0, 'content': 'TUYỂN DỤNG THỰC TẬP SINH BACKEND\n\nCông ty CP Đầu tư và Thương mại Redon đang tìm kiếm Thực tập sinh Backend tiềm\n\nnăng để cùng đồng hành và phát  ... (1222 characters truncated) ... quan nếu có) về: <EMAIL>\n\nvới tiêu đề ghi rõ: [TTS Frontend] – Họ và Tên\n\n\uf0fc\n\nHoặc liên hệ qua Zalo bằng số điện thoại: 0865475477', 'chunk_metadata': '{}', 'vector_store_id': 'vs_1cbb5304ceae85e591e5268198095df0', 'embedding': None, 'embedding_source': 'gemini', 'embedding_dimensions': 2048, 'created_at': 1747982828953, 'updated_at': 1747982828953}]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-23 13:47:08 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-IRqDdjNnqc2ndz5j: 100% - Lỗi xử lý tập tin
2025-05-23 13:47:08 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-IRqDdjNnqc2ndz5j_2e660f49-962f-46ca-9cee-464259cf9e9c
2025-05-23 13:49:43 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 13:49:43 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 13:49:43 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 13:49:43 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 13:49:58 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 13:49:59 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 13:49:59 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 13:49:59 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 13:49:59 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 13:50:03 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 13:50:03 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 13:50:03 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 13:50:03 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 13:50:31 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-d6b0...
2025-05-23 13:50:31 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-d6b0... (length: 42)
2025-05-23 13:50:31 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-Ft-1-Mor7eiBZfm7, 34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano Ronaldo
2025-05-23 13:50:31 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-Ft-1-Mor7eiBZfm7: 0% - Đang bắt đầu xử lý tập tin
2025-05-23 13:50:31 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-Ft-1-Mor7eiBZfm7: 5% - Đang tải tập tin
2025-05-23 13:50:31 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano_Ronaldo
2025-05-23 13:50:31 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-Ft-1-Mor7eiBZfm7_6b9a8b6e-ea48-435f-80a9-f1e84e9489d1
2025-05-23 13:50:31 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-Ft-1-Mor7eiBZfm7_6b9a8b6e-ea48-435f-80a9-f1e84e9489d1
2025-05-23 13:50:31 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-Ft-1-Mor7eiBZfm7_6b9a8b6e-ea48-435f-80a9-f1e84e9489d1
2025-05-23 13:50:31 - api.file_endpoints - INFO - [file_endpoints.py:286] - Using vector store: meo-moe-meoooooooo (vs_1cbb5304ceae85e591e5268198095df0)
2025-05-23 13:50:31 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.1720s
2025-05-23 13:50:31 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-Ft-1-Mor7eiBZfm7: 5% - Đang xử lý tập tin với kho vector: meo-moe-meoooooooo
2025-05-23 13:50:31 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-Ft-1-Mor7eiBZfm7: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-23 13:50:31 - services.markdown_service - INFO - [markdown_service.py:32] - Converting file to Markdown: temp-s3\temp_file-Ft-1-Mor7eiBZfm7_6b9a8b6e-ea48-435f-80a9-f1e84e9489d1 (type: )
2025-05-23 13:50:31 - services.markdown_service - WARNING - [markdown_service.py:112] - Unsupported file type: , attempting generic conversion
2025-05-23 13:50:31 - services.markdown_service - INFO - [markdown_service.py:123] - Successfully converted file to Markdown (1369 characters)
2025-05-23 13:50:31 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-Ft-1-Mor7eiBZfm7: 30% - Đang chia nhỏ nội dung
2025-05-23 13:50:31 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-23 13:50:31 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 1 well-sized chunks
2025-05-23 13:50:31 - services.document_chunks_service - INFO - [document_chunks_service.py:66] - Split content into 1 chunks for file file-Ft-1-Mor7eiBZfm7
2025-05-23 13:50:31 - services.document_chunks_service - INFO - [document_chunks_service.py:80] - Associating chunks with vector store: vs_1cbb5304ceae85e591e5268198095df0 (meo-moe-meoooooooo)
2025-05-23 13:50:31 - services.document_chunks_service - INFO - [document_chunks_service.py:104] - Successfully saved 1 chunks for file file-Ft-1-Mor7eiBZfm7
2025-05-23 13:50:31 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-Ft-1-Mor7eiBZfm7: 50% - Đang tạo embeddings cho 1 đoạn và lưu vào kho vector meo-moe-meoooooooo
2025-05-23 13:50:31 - services.document_chunks_service - INFO - [document_chunks_service.py:154] - Creating embeddings for 1 chunks with 5 parallel workers
2025-05-23 13:50:31 - services.document_chunks_service - INFO - [document_chunks_service.py:159] - Processing batch 1 (1 chunks)
2025-05-23 13:50:33 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-23 13:50:33 - services.document_chunks_service - ERROR - [document_chunks_service.py:254] - Error creating embeddings in parallel: name 'time' is not defined
2025-05-23 13:50:33 - services.document_chunks_service - INFO - [document_chunks_service.py:257] - Falling back to sequential embedding creation
2025-05-23 13:50:34 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-23 13:50:34 - services.document_chunks_service - ERROR - [document_chunks_service.py:306] - Error creating embedding for chunk beca3fdd-5e6c-48a2-9e11-cce34dc94652: name 'time' is not defined
2025-05-23 13:50:34 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-Ft-1-Mor7eiBZfm7: 100% - Hoàn thành xử lý tập tin với 1/1 đoạn
2025-05-23 13:50:34 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-Ft-1-Mor7eiBZfm7_6b9a8b6e-ea48-435f-80a9-f1e84e9489d1
2025-05-23 14:00:01 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 14:00:06 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 14:00:06 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 14:00:06 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 14:00:06 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 14:00:30 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 14:00:35 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 14:00:35 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 14:00:35 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 14:00:35 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 14:00:45 - main - INFO - [main.py:176] - Request to /docs completed in 0.0007s
2025-05-23 14:00:46 - main - INFO - [main.py:176] - Request to /openapi.json completed in 0.0244s
2025-05-23 14:00:51 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-d6b0...
2025-05-23 14:00:51 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-d6b0... (length: 42)
2025-05-23 14:00:51 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-4KDIW6oyiOjB-H9S, 34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano Ronaldo
2025-05-23 14:00:51 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-4KDIW6oyiOjB-H9S: 0% - Đang bắt đầu xử lý tập tin
2025-05-23 14:00:51 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-4KDIW6oyiOjB-H9S: 5% - Đang tải tập tin
2025-05-23 14:00:51 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano_Ronaldo
2025-05-23 14:00:51 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-4KDIW6oyiOjB-H9S_4b11fa19-cbf6-4d60-9ed6-28754ae01a6b
2025-05-23 14:00:52 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-4KDIW6oyiOjB-H9S_4b11fa19-cbf6-4d60-9ed6-28754ae01a6b
2025-05-23 14:00:52 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-4KDIW6oyiOjB-H9S_4b11fa19-cbf6-4d60-9ed6-28754ae01a6b
2025-05-23 14:00:52 - api.file_endpoints - INFO - [file_endpoints.py:286] - Using vector store: meo-moe-meoooooooo (vs_1cbb5304ceae85e591e5268198095df0)
2025-05-23 14:00:52 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.4862s
2025-05-23 14:00:52 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-4KDIW6oyiOjB-H9S: 5% - Đang xử lý tập tin với kho vector: meo-moe-meoooooooo
2025-05-23 14:00:52 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-4KDIW6oyiOjB-H9S: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-23 14:00:52 - services.markdown_service - INFO - [markdown_service.py:32] - Converting file to Markdown: temp-s3\temp_file-4KDIW6oyiOjB-H9S_4b11fa19-cbf6-4d60-9ed6-28754ae01a6b (type: )
2025-05-23 14:00:52 - services.markdown_service - WARNING - [markdown_service.py:112] - Unsupported file type: , attempting generic conversion
2025-05-23 14:00:52 - services.markdown_service - INFO - [markdown_service.py:123] - Successfully converted file to Markdown (1369 characters)
2025-05-23 14:00:52 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-4KDIW6oyiOjB-H9S: 30% - Đang chia nhỏ nội dung
2025-05-23 14:00:52 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-23 14:00:52 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 1 well-sized chunks
2025-05-23 14:00:52 - services.document_chunks_service - INFO - [document_chunks_service.py:67] - Split content into 1 chunks for file file-4KDIW6oyiOjB-H9S
2025-05-23 14:00:52 - services.document_chunks_service - INFO - [document_chunks_service.py:81] - Associating chunks with vector store: vs_1cbb5304ceae85e591e5268198095df0 (meo-moe-meoooooooo)
2025-05-23 14:00:52 - services.document_chunks_service - INFO - [document_chunks_service.py:115] - Updated storage for vector store vs_1cbb5304ceae85e591e5268198095df0 by adding 1738 bytes
2025-05-23 14:00:52 - services.document_chunks_service - INFO - [document_chunks_service.py:124] - Successfully saved 1 chunks for file file-4KDIW6oyiOjB-H9S
2025-05-23 14:00:52 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-4KDIW6oyiOjB-H9S: 50% - Đang tạo embeddings cho 1 đoạn và lưu vào kho vector meo-moe-meoooooooo
2025-05-23 14:00:52 - services.document_chunks_service - INFO - [document_chunks_service.py:174] - Creating embeddings for 1 chunks with 5 parallel workers
2025-05-23 14:00:52 - services.document_chunks_service - INFO - [document_chunks_service.py:179] - Processing batch 1 (1 chunks)
2025-05-23 14:00:54 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-23 14:00:54 - services.document_chunks_service - INFO - [document_chunks_service.py:270] - Updated storage for 1 vector stores
2025-05-23 14:00:54 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-4KDIW6oyiOjB-H9S: 100% - Hoàn thành xử lý tập tin với 1/1 đoạn
2025-05-23 14:00:54 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-4KDIW6oyiOjB-H9S_4b11fa19-cbf6-4d60-9ed6-28754ae01a6b
2025-05-23 14:10:33 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 14:10:38 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 14:10:38 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 14:10:38 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 14:10:38 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 14:15:36 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 14:15:39 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 14:15:39 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 14:15:39 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 14:15:39 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 14:18:06 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 14:18:10 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 14:18:10 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 14:18:10 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 14:18:10 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 14:28:07 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 14:28:12 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 14:28:12 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 14:28:12 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 14:28:12 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 14:34:37 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-07fd...
2025-05-23 14:34:37 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-23 14:34:37 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-Qemrrrh38B7eXNxG, 34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano Ronaldo
2025-05-23 14:34:37 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-Qemrrrh38B7eXNxG: 0% - Đang bắt đầu xử lý tập tin
2025-05-23 14:34:37 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-Qemrrrh38B7eXNxG: 5% - Đang tải tập tin
2025-05-23 14:34:37 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano_Ronaldo
2025-05-23 14:34:37 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-Qemrrrh38B7eXNxG_a7fda419-c1cc-4271-bf55-241c784e0a6b
2025-05-23 14:34:37 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-Qemrrrh38B7eXNxG_a7fda419-c1cc-4271-bf55-241c784e0a6b
2025-05-23 14:34:37 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-Qemrrrh38B7eXNxG_a7fda419-c1cc-4271-bf55-241c784e0a6b
2025-05-23 14:34:37 - api.file_endpoints - WARNING - [file_endpoints.py:288] - Vector store not found: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-23 14:34:37 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.8971s
2025-05-23 14:34:37 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-Qemrrrh38B7eXNxG: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-23 14:34:37 - services.markdown_service - INFO - [markdown_service.py:32] - Converting file to Markdown: temp-s3\temp_file-Qemrrrh38B7eXNxG_a7fda419-c1cc-4271-bf55-241c784e0a6b (type: )
2025-05-23 14:34:38 - services.markdown_service - WARNING - [markdown_service.py:112] - Unsupported file type: , attempting generic conversion
2025-05-23 14:34:38 - services.markdown_service - INFO - [markdown_service.py:123] - Successfully converted file to Markdown (1369 characters)
2025-05-23 14:34:38 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-Qemrrrh38B7eXNxG: 30% - Đang chia nhỏ nội dung
2025-05-23 14:34:38 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-23 14:34:38 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 1 well-sized chunks
2025-05-23 14:34:38 - services.document_chunks_service - INFO - [document_chunks_service.py:67] - Split content into 1 chunks for file file-Qemrrrh38B7eXNxG
2025-05-23 14:34:38 - services.document_chunks_service - INFO - [document_chunks_service.py:153] - Successfully saved 1 chunks for file file-Qemrrrh38B7eXNxG
2025-05-23 14:34:38 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-Qemrrrh38B7eXNxG: 50% - Đang tạo embeddings cho 1 đoạn
2025-05-23 14:34:38 - services.document_chunks_service - INFO - [document_chunks_service.py:203] - Creating embeddings for 1 chunks with 5 parallel workers
2025-05-23 14:34:38 - services.document_chunks_service - INFO - [document_chunks_service.py:208] - Processing batch 1 (1 chunks)
2025-05-23 14:34:40 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-23 14:34:40 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-Qemrrrh38B7eXNxG: 100% - Hoàn thành xử lý tập tin với 1/1 đoạn
2025-05-23 14:34:40 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-Qemrrrh38B7eXNxG_a7fda419-c1cc-4271-bf55-241c784e0a6b
2025-05-23 14:34:40 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/file-Qemrrrh38B7eXNxG/progress with API key: redai-07fd...
2025-05-23 14:34:40 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-23 14:34:40 - main - INFO - [main.py:176] - Request to /api/files/file-Qemrrrh38B7eXNxG/progress completed in 0.0065s
2025-05-23 14:35:41 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 14:35:48 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 14:35:48 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 14:35:48 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 14:35:48 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 14:44:14 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/vector-stores/vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe/files with API key: redai-07fd...
2025-05-23 14:44:14 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-23 14:44:14 - main - INFO - [main.py:176] - Request to /api/vector-stores/vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe/files completed in 0.0993s
2025-05-23 14:45:43 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 14:54:39 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 14:54:39 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 14:54:39 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 14:54:39 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 15:02:00 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/vector-stores/vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe/files with API key: redai-07fd...
2025-05-23 15:02:00 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-23 15:02:00 - main - INFO - [main.py:176] - Request to /api/vector-stores/vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe/files completed in 0.1073s
2025-05-23 15:07:42 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 15:07:46 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 15:07:46 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 15:07:46 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 15:07:46 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 15:13:41 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-07fd...
2025-05-23 15:13:41 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-23 15:13:42 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-gVS9xSEzfkuBo-sj, 34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano Ronaldo
2025-05-23 15:13:42 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-gVS9xSEzfkuBo-sj: 0% - Đang bắt đầu xử lý tập tin
2025-05-23 15:13:42 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-gVS9xSEzfkuBo-sj: 5% - Đang tải tập tin
2025-05-23 15:13:42 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano_Ronaldo
2025-05-23 15:13:42 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-gVS9xSEzfkuBo-sj_c67128ef-c1fb-4ee8-b75e-9bb9e1ceeaa9
2025-05-23 15:13:42 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-gVS9xSEzfkuBo-sj_c67128ef-c1fb-4ee8-b75e-9bb9e1ceeaa9
2025-05-23 15:13:42 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-gVS9xSEzfkuBo-sj_c67128ef-c1fb-4ee8-b75e-9bb9e1ceeaa9
2025-05-23 15:13:42 - api.file_endpoints - WARNING - [file_endpoints.py:288] - Vector store not found: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-23 15:13:42 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.7163s
2025-05-23 15:13:42 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-gVS9xSEzfkuBo-sj: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-23 15:13:42 - services.markdown_service - INFO - [markdown_service.py:32] - Converting file to Markdown: temp-s3\temp_file-gVS9xSEzfkuBo-sj_c67128ef-c1fb-4ee8-b75e-9bb9e1ceeaa9 (type: )
2025-05-23 15:13:42 - services.markdown_service - WARNING - [markdown_service.py:112] - Unsupported file type: , attempting generic conversion
2025-05-23 15:13:42 - services.markdown_service - INFO - [markdown_service.py:123] - Successfully converted file to Markdown (1369 characters)
2025-05-23 15:13:42 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-gVS9xSEzfkuBo-sj: 30% - Đang chia nhỏ nội dung
2025-05-23 15:13:42 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-23 15:13:42 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 1 well-sized chunks
2025-05-23 15:13:42 - services.document_chunks_service - INFO - [document_chunks_service.py:67] - Split content into 1 chunks for file file-gVS9xSEzfkuBo-sj
2025-05-23 15:13:42 - services.document_chunks_service - INFO - [document_chunks_service.py:153] - Successfully saved 1 chunks for file file-gVS9xSEzfkuBo-sj
2025-05-23 15:13:42 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-gVS9xSEzfkuBo-sj: 50% - Đang tạo embeddings cho 1 đoạn
2025-05-23 15:13:42 - services.document_chunks_service - INFO - [document_chunks_service.py:203] - Creating embeddings for 1 chunks with 5 parallel workers
2025-05-23 15:13:42 - services.document_chunks_service - INFO - [document_chunks_service.py:208] - Processing batch 1 (1 chunks)
2025-05-23 15:13:45 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-23 15:13:45 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-gVS9xSEzfkuBo-sj: 100% - Hoàn thành xử lý tập tin với 1/1 đoạn
2025-05-23 15:13:45 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-gVS9xSEzfkuBo-sj_c67128ef-c1fb-4ee8-b75e-9bb9e1ceeaa9
2025-05-23 15:13:45 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/file-gVS9xSEzfkuBo-sj/progress with API key: redai-07fd...
2025-05-23 15:13:45 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-23 15:13:45 - main - INFO - [main.py:176] - Request to /api/files/file-gVS9xSEzfkuBo-sj/progress completed in 0.0079s
2025-05-23 15:49:45 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 15:56:51 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 15:56:51 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 15:56:51 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 15:56:51 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 15:57:08 - main - INFO - [main.py:176] - Request to /docs completed in 0.0007s
2025-05-23 15:57:08 - main - INFO - [main.py:176] - Request to /openapi.json completed in 0.0162s
2025-05-23 15:58:40 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 16:22:54 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 16:22:54 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 16:22:54 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 16:22:54 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 16:26:12 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-07fd...
2025-05-23 16:26:12 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-23 16:26:12 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-kn7Bjo0YbH67w2IV, 34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano Ronaldo
2025-05-23 16:26:12 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-kn7Bjo0YbH67w2IV: 0% - Đang bắt đầu xử lý tập tin
2025-05-23 16:26:12 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-kn7Bjo0YbH67w2IV: 5% - Đang tải tập tin
2025-05-23 16:26:12 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano_Ronaldo
2025-05-23 16:26:12 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-kn7Bjo0YbH67w2IV_4fd8d69e-b9c3-45d4-a1df-31699eaa50a4
2025-05-23 16:26:12 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-kn7Bjo0YbH67w2IV_4fd8d69e-b9c3-45d4-a1df-31699eaa50a4
2025-05-23 16:26:12 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-kn7Bjo0YbH67w2IV_4fd8d69e-b9c3-45d4-a1df-31699eaa50a4
2025-05-23 16:26:12 - api.file_endpoints - WARNING - [file_endpoints.py:288] - Vector store not found: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-23 16:26:12 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.7355s
2025-05-23 16:26:12 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-kn7Bjo0YbH67w2IV: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-23 16:26:12 - services.markdown_service - INFO - [markdown_service.py:32] - Converting file to Markdown: temp-s3\temp_file-kn7Bjo0YbH67w2IV_4fd8d69e-b9c3-45d4-a1df-31699eaa50a4 (type: )
2025-05-23 16:26:13 - services.markdown_service - WARNING - [markdown_service.py:112] - Unsupported file type: , attempting generic conversion
2025-05-23 16:26:13 - services.markdown_service - INFO - [markdown_service.py:123] - Successfully converted file to Markdown (1369 characters)
2025-05-23 16:26:13 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-kn7Bjo0YbH67w2IV: 30% - Đang chia nhỏ nội dung
2025-05-23 16:26:13 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-23 16:26:13 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 1 well-sized chunks
2025-05-23 16:26:13 - services.document_chunks_service - INFO - [document_chunks_service.py:67] - Split content into 1 chunks for file file-kn7Bjo0YbH67w2IV
2025-05-23 16:26:13 - services.document_chunks_service - INFO - [document_chunks_service.py:153] - Successfully saved 1 chunks for file file-kn7Bjo0YbH67w2IV
2025-05-23 16:26:13 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-kn7Bjo0YbH67w2IV: 50% - Đang tạo embeddings cho 1 đoạn
2025-05-23 16:26:13 - services.document_chunks_service - INFO - [document_chunks_service.py:203] - Creating embeddings for 1 chunks with 5 parallel workers
2025-05-23 16:26:13 - services.document_chunks_service - INFO - [document_chunks_service.py:208] - Processing batch 1 (1 chunks)
2025-05-23 16:26:16 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-23 16:26:16 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-kn7Bjo0YbH67w2IV: 100% - Hoàn thành xử lý tập tin với 1/1 đoạn
2025-05-23 16:26:16 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-kn7Bjo0YbH67w2IV_4fd8d69e-b9c3-45d4-a1df-31699eaa50a4
2025-05-23 16:29:49 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 16:29:58 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 16:29:58 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 16:29:58 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 16:29:58 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 16:30:34 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-07fd...
2025-05-23 16:30:34 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-23 16:30:35 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-2TBymHhroToxc8JR, 34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano Ronaldo
2025-05-23 16:30:35 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-2TBymHhroToxc8JR: 0% - Đang bắt đầu xử lý tập tin
2025-05-23 16:30:35 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-2TBymHhroToxc8JR: 5% - Đang tải tập tin
2025-05-23 16:30:35 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano_Ronaldo
2025-05-23 16:30:35 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-2TBymHhroToxc8JR_8bf7113b-7fd3-42b2-9a18-a80acf589b9f
2025-05-23 16:30:35 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-2TBymHhroToxc8JR_8bf7113b-7fd3-42b2-9a18-a80acf589b9f
2025-05-23 16:30:35 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-2TBymHhroToxc8JR_8bf7113b-7fd3-42b2-9a18-a80acf589b9f
2025-05-23 16:30:35 - api.file_endpoints - WARNING - [file_endpoints.py:288] - Vector store not found: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-23 16:30:35 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.2770s
2025-05-23 16:30:35 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-2TBymHhroToxc8JR: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-23 16:30:35 - services.markdown_service - INFO - [markdown_service.py:32] - Converting file to Markdown: temp-s3\temp_file-2TBymHhroToxc8JR_8bf7113b-7fd3-42b2-9a18-a80acf589b9f (type: )
2025-05-23 16:30:35 - services.markdown_service - WARNING - [markdown_service.py:112] - Unsupported file type: , attempting generic conversion
2025-05-23 16:30:35 - services.markdown_service - INFO - [markdown_service.py:123] - Successfully converted file to Markdown (1369 characters)
2025-05-23 16:30:35 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-2TBymHhroToxc8JR: 30% - Đang chia nhỏ nội dung
2025-05-23 16:30:35 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-23 16:30:35 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 1 well-sized chunks
2025-05-23 16:30:35 - services.document_chunks_service - INFO - [document_chunks_service.py:67] - Split content into 1 chunks for file file-2TBymHhroToxc8JR
2025-05-23 16:30:35 - services.document_chunks_service - ERROR - [document_chunks_service.py:158] - Database error creating chunks: (psycopg2.errors.NotNullViolation) null value in column "vector_store_id" of relation "document_chunks" violates not-null constraint
DETAIL:  Failing row contains (d7d7371e-5a86-46c1-ade9-b38decfd5266, file-2TBymHhroToxc8JR, 0, TUYỂN DỤNG THỰC TẬP SINH BACKEND

Công ty CP Đầu tư..., {}, null, 1747992635332, 1747992635332, gemini, 2048, null).

[SQL: INSERT INTO document_chunks (id, file_id, chunk_index, content, chunk_metadata, vector_store_id, embedding, embedding_source, embedding_dimensions, created_at, updated_at) VALUES (%(id)s::UUID, %(file_id)s, %(chunk_index)s, %(content)s, %(chunk_metadata)s::JSONB, %(vector_store_id)s, %(embedding)s, %(embedding_source)s, %(embedding_dimensions)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('d7d7371e-5a86-46c1-ade9-b38decfd5266'), 'file_id': 'file-2TBymHhroToxc8JR', 'chunk_index': 0, 'content': 'TUYỂN DỤNG THỰC TẬP SINH BACKEND\n\nCông ty CP Đầu tư và Thương mại Redon đang tìm kiếm Thực tập sinh Backend tiềm\n\nnăng để cùng đồng hành và phát  ... (1222 characters truncated) ... quan nếu có) về: <EMAIL>\n\nvới tiêu đề ghi rõ: [TTS Frontend] – Họ và Tên\n\n\uf0fc\n\nHoặc liên hệ qua Zalo bằng số điện thoại: 0865475477', 'chunk_metadata': '{}', 'vector_store_id': None, 'embedding': None, 'embedding_source': 'gemini', 'embedding_dimensions': 2048, 'created_at': 1747992635332, 'updated_at': 1747992635332}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-23 16:30:35 - api.file_endpoints - ERROR - [file_endpoints.py:201] - Error processing file file-2TBymHhroToxc8JR: (psycopg2.errors.NotNullViolation) null value in column "vector_store_id" of relation "document_chunks" violates not-null constraint
DETAIL:  Failing row contains (d7d7371e-5a86-46c1-ade9-b38decfd5266, file-2TBymHhroToxc8JR, 0, TUYỂN DỤNG THỰC TẬP SINH BACKEND

Công ty CP Đầu tư..., {}, null, 1747992635332, 1747992635332, gemini, 2048, null).

[SQL: INSERT INTO document_chunks (id, file_id, chunk_index, content, chunk_metadata, vector_store_id, embedding, embedding_source, embedding_dimensions, created_at, updated_at) VALUES (%(id)s::UUID, %(file_id)s, %(chunk_index)s, %(content)s, %(chunk_metadata)s::JSONB, %(vector_store_id)s, %(embedding)s, %(embedding_source)s, %(embedding_dimensions)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('d7d7371e-5a86-46c1-ade9-b38decfd5266'), 'file_id': 'file-2TBymHhroToxc8JR', 'chunk_index': 0, 'content': 'TUYỂN DỤNG THỰC TẬP SINH BACKEND\n\nCông ty CP Đầu tư và Thương mại Redon đang tìm kiếm Thực tập sinh Backend tiềm\n\nnăng để cùng đồng hành và phát  ... (1222 characters truncated) ... quan nếu có) về: <EMAIL>\n\nvới tiêu đề ghi rõ: [TTS Frontend] – Họ và Tên\n\n\uf0fc\n\nHoặc liên hệ qua Zalo bằng số điện thoại: 0865475477', 'chunk_metadata': '{}', 'vector_store_id': None, 'embedding': None, 'embedding_source': 'gemini', 'embedding_dimensions': 2048, 'created_at': 1747992635332, 'updated_at': 1747992635332}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-23 16:30:35 - api.file_endpoints - INFO - [file_endpoints.py:102] - Progress update for file-2TBymHhroToxc8JR: 100% - Lỗi xử lý tập tin
2025-05-23 16:30:35 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-2TBymHhroToxc8JR_8bf7113b-7fd3-42b2-9a18-a80acf589b9f
2025-05-23 16:39:55 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 16:39:58 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 16:39:58 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 16:39:58 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 16:39:58 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 16:44:56 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 16:44:58 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 16:44:58 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 16:44:58 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 16:44:58 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 16:45:01 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 16:45:01 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 16:45:01 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 16:45:01 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 16:45:48 - main - INFO - [main.py:114] - Shutting down the application
2025-05-23 16:45:54 - main - INFO - [main.py:97] - Starting up the application
2025-05-23 16:45:54 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-23 16:45:54 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-23 16:45:54 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-23 16:47:06 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-07fd...
2025-05-23 16:47:06 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-23 16:47:06 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-xHNFXW4sNu9Ro9Xr, 34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano Ronaldo
2025-05-23 16:47:06 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-xHNFXW4sNu9Ro9Xr: 0% - Đang bắt đầu xử lý tập tin
2025-05-23 16:47:06 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-xHNFXW4sNu9Ro9Xr: 5% - Đang tải tập tin
2025-05-23 16:47:06 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/34af6a9f-47c7-4012-8c78-4099b719689b-Cristiano_Ronaldo
2025-05-23 16:47:06 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-xHNFXW4sNu9Ro9Xr_d842573c-887b-4cbf-80d5-dbb718bc79e8
2025-05-23 16:47:06 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-xHNFXW4sNu9Ro9Xr_d842573c-887b-4cbf-80d5-dbb718bc79e8
2025-05-23 16:47:06 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-xHNFXW4sNu9Ro9Xr_d842573c-887b-4cbf-80d5-dbb718bc79e8
2025-05-23 16:47:06 - api.file_endpoints - INFO - [file_endpoints.py:283] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-23 16:47:06 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.5008s
2025-05-23 16:47:06 - api.file_endpoints - INFO - [file_endpoints.py:135] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-23 16:47:06 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-xHNFXW4sNu9Ro9Xr: 5% - Đang xử lý tập tin với kho vector: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-23 16:47:06 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-xHNFXW4sNu9Ro9Xr: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-23 16:47:06 - services.markdown_service - INFO - [markdown_service.py:32] - Converting file to Markdown: temp-s3\temp_file-xHNFXW4sNu9Ro9Xr_d842573c-887b-4cbf-80d5-dbb718bc79e8 (type: )
2025-05-23 16:47:06 - services.markdown_service - WARNING - [markdown_service.py:112] - Unsupported file type: , attempting generic conversion
2025-05-23 16:47:06 - services.markdown_service - INFO - [markdown_service.py:123] - Successfully converted file to Markdown (1369 characters)
2025-05-23 16:47:06 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-xHNFXW4sNu9Ro9Xr: 30% - Đang chia nhỏ nội dung
2025-05-23 16:47:06 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-23 16:47:06 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 1 well-sized chunks
2025-05-23 16:47:06 - services.document_chunks_service - INFO - [document_chunks_service.py:67] - Split content into 1 chunks for file file-xHNFXW4sNu9Ro9Xr
2025-05-23 16:47:06 - services.document_chunks_service - INFO - [document_chunks_service.py:84] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-23 16:47:06 - services.document_chunks_service - INFO - [document_chunks_service.py:118] - Successfully saved 1 chunks for file file-xHNFXW4sNu9Ro9Xr
2025-05-23 16:47:06 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-xHNFXW4sNu9Ro9Xr: 50% - Đang tạo embeddings cho 1 đoạn và lưu vào kho vector vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-23 16:47:06 - services.document_chunks_service - INFO - [document_chunks_service.py:167] - Creating embeddings for 1 chunks with 5 parallel workers
2025-05-23 16:47:06 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 1 (1 chunks)
2025-05-23 16:47:08 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-23 16:47:08 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-xHNFXW4sNu9Ro9Xr: 100% - Hoàn thành xử lý tập tin với 1/1 đoạn
2025-05-23 16:47:08 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-xHNFXW4sNu9Ro9Xr_d842573c-887b-4cbf-80d5-dbb718bc79e8
2025-05-23 16:51:34 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 13:34:48 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 13:34:48 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 13:34:49 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 13:34:49 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 13:36:15 - main - INFO - [main.py:176] - Request to /docs completed in 0.0009s
2025-05-24 13:36:15 - main - INFO - [main.py:176] - Request to /openapi.json completed in 0.0116s
2025-05-24 13:37:10 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-0648...
2025-05-24 13:37:10 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-0648... (length: 38)
2025-05-24 13:37:10 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-vjDTU2ty_IPYAsz9, 1748068115726-82cfe3f1-10ad-489f-981e-41689244fc55.pdf
2025-05-24 13:37:10 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-vjDTU2ty_IPYAsz9: 0% - Đang bắt đầu xử lý tập tin
2025-05-24 13:37:10 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-vjDTU2ty_IPYAsz9: 5% - Đang tải tập tin
2025-05-24 13:37:10 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/1748068115726-82cfe3f1-10ad-489f-981e-41689244fc55.pdf
2025-05-24 13:37:10 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-vjDTU2ty_IPYAsz9_ddc272a9-b2e2-4088-bd0c-677cb9a250e7.pdf
2025-05-24 13:37:10 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-vjDTU2ty_IPYAsz9_ddc272a9-b2e2-4088-bd0c-677cb9a250e7.pdf
2025-05-24 13:37:10 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-vjDTU2ty_IPYAsz9_ddc272a9-b2e2-4088-bd0c-677cb9a250e7.pdf
2025-05-24 13:37:10 - api.file_endpoints - INFO - [file_endpoints.py:283] - Sử dụng vector store ID: vs_681c63f7b8d48191a3394cb3b025b0e7
2025-05-24 13:37:10 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.1934s
2025-05-24 13:37:10 - api.file_endpoints - INFO - [file_endpoints.py:135] - Sử dụng vector store ID: vs_681c63f7b8d48191a3394cb3b025b0e7
2025-05-24 13:37:10 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-vjDTU2ty_IPYAsz9: 5% - Đang xử lý tập tin với kho vector: vs_681c63f7b8d48191a3394cb3b025b0e7
2025-05-24 13:37:10 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-vjDTU2ty_IPYAsz9: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-24 13:37:10 - services.markdown_service - INFO - [markdown_service.py:32] - Converting file to Markdown: temp-s3\temp_file-vjDTU2ty_IPYAsz9_ddc272a9-b2e2-4088-bd0c-677cb9a250e7.pdf (type: .pdf)
2025-05-24 13:37:10 - services.markdown_service - INFO - [markdown_service.py:41] - Converting PDF to Markdown using OCR
2025-05-24 13:37:10 - services.image_processing_service - INFO - [image_processing_service.py:255] - Processing PDF with OCR: temp-s3\temp_file-vjDTU2ty_IPYAsz9_ddc272a9-b2e2-4088-bd0c-677cb9a250e7.pdf
2025-05-24 13:37:10 - services.image_processing_service - INFO - [image_processing_service.py:39] - Converting PDF to images: temp-s3\temp_file-vjDTU2ty_IPYAsz9_ddc272a9-b2e2-4088-bd0c-677cb9a250e7.pdf (DPI: 300)
2025-05-24 13:37:10 - services.image_processing_service - ERROR - [image_processing_service.py:76] - Error converting PDF to images: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 13:37:10 - services.image_processing_service - ERROR - [image_processing_service.py:294] - Error processing PDF with OCR: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 13:37:10 - services.markdown_service - ERROR - [markdown_service.py:127] - Error converting file to Markdown: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 13:37:10 - api.file_endpoints - ERROR - [file_endpoints.py:191] - Error processing file file-vjDTU2ty_IPYAsz9: Failed to convert file to Markdown: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 13:37:10 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-vjDTU2ty_IPYAsz9: 100% - Lỗi xử lý tập tin
2025-05-24 13:37:10 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-vjDTU2ty_IPYAsz9_ddc272a9-b2e2-4088-bd0c-677cb9a250e7.pdf
2025-05-24 13:39:40 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 13:39:43 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 13:39:43 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 13:39:43 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 13:39:43 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 13:42:53 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 13:42:55 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 13:42:55 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 13:42:55 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 13:42:55 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 13:43:20 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 13:43:20 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 13:43:20 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 13:43:20 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 13:43:29 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-0648...
2025-05-24 13:43:29 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-0648... (length: 38)
2025-05-24 13:43:29 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-5WTRfypFVnSj--dv, 1748068115726-82cfe3f1-10ad-489f-981e-41689244fc55.pdf
2025-05-24 13:43:29 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-5WTRfypFVnSj--dv: 0% - Đang bắt đầu xử lý tập tin
2025-05-24 13:43:29 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-5WTRfypFVnSj--dv: 5% - Đang tải tập tin
2025-05-24 13:43:29 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/1748068115726-82cfe3f1-10ad-489f-981e-41689244fc55.pdf
2025-05-24 13:43:29 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-5WTRfypFVnSj--dv_0c4bdb45-2f6e-4d0c-bbea-00b22fc24220.pdf
2025-05-24 13:43:29 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-5WTRfypFVnSj--dv_0c4bdb45-2f6e-4d0c-bbea-00b22fc24220.pdf
2025-05-24 13:43:29 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-5WTRfypFVnSj--dv_0c4bdb45-2f6e-4d0c-bbea-00b22fc24220.pdf
2025-05-24 13:43:29 - api.file_endpoints - INFO - [file_endpoints.py:283] - Sử dụng vector store ID: vs_681c63f7b8d48191a3394cb3b025b0e7
2025-05-24 13:43:29 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.2789s
2025-05-24 13:43:29 - api.file_endpoints - INFO - [file_endpoints.py:135] - Sử dụng vector store ID: vs_681c63f7b8d48191a3394cb3b025b0e7
2025-05-24 13:43:29 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-5WTRfypFVnSj--dv: 5% - Đang xử lý tập tin với kho vector: vs_681c63f7b8d48191a3394cb3b025b0e7
2025-05-24 13:43:29 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-5WTRfypFVnSj--dv: 10% - Đang chuyển đổi tập tin sang Markdown
2025-05-24 13:43:29 - services.markdown_service - INFO - [markdown_service.py:71] - Converting file to Markdown: temp-s3\temp_file-5WTRfypFVnSj--dv_0c4bdb45-2f6e-4d0c-bbea-00b22fc24220.pdf (type: .pdf)
2025-05-24 13:43:29 - services.markdown_service - INFO - [markdown_service.py:138] - Converting PDF to Markdown using OCR
2025-05-24 13:43:29 - services.image_processing_service - INFO - [image_processing_service.py:329] - Processing PDF with OCR: temp-s3\temp_file-5WTRfypFVnSj--dv_0c4bdb45-2f6e-4d0c-bbea-00b22fc24220.pdf
2025-05-24 13:43:29 - services.image_processing_service - INFO - [image_processing_service.py:60] - Converting PDF to images: temp-s3\temp_file-5WTRfypFVnSj--dv_0c4bdb45-2f6e-4d0c-bbea-00b22fc24220.pdf (DPI: 300)
2025-05-24 13:43:29 - services.image_processing_service - WARNING - [image_processing_service.py:35] - PDF validation failed: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 13:43:29 - services.image_processing_service - WARNING - [image_processing_service.py:64] - PDF validation failed, attempting conversion anyway
2025-05-24 13:43:29 - services.image_processing_service - WARNING - [image_processing_service.py:88] - Standard PDF conversion failed: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 13:43:29 - services.image_processing_service - INFO - [image_processing_service.py:92] - Trying with lower DPI (150)
2025-05-24 13:43:29 - services.image_processing_service - WARNING - [image_processing_service.py:101] - Lower DPI conversion failed: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 13:43:29 - services.image_processing_service - INFO - [image_processing_service.py:105] - Trying basic conversion
2025-05-24 13:43:29 - services.image_processing_service - ERROR - [image_processing_service.py:115] - All PDF conversion methods failed: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 13:43:29 - services.image_processing_service - ERROR - [image_processing_service.py:150] - Error converting PDF to images: Could not convert PDF to images. Last error: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 13:43:29 - services.image_processing_service - ERROR - [image_processing_service.py:368] - Error processing PDF with OCR: Could not convert PDF to images. Last error: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 13:43:29 - services.markdown_service - WARNING - [markdown_service.py:154] - OCR failed: Could not convert PDF to images. Last error: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table

, falling back to markitdown
2025-05-24 13:43:29 - services.markdown_service - INFO - [markdown_service.py:157] - Converting PDF to Markdown using markitdown
2025-05-24 13:43:29 - services.markdown_service - INFO - [markdown_service.py:126] - Successfully converted file to Markdown (15919 characters)
2025-05-24 13:43:29 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-5WTRfypFVnSj--dv: 30% - Đang chia nhỏ nội dung
2025-05-24 13:43:29 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-24 13:43:29 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 10 well-sized chunks
2025-05-24 13:43:29 - services.document_chunks_service - INFO - [document_chunks_service.py:67] - Split content into 10 chunks for file file-5WTRfypFVnSj--dv
2025-05-24 13:43:29 - services.document_chunks_service - INFO - [document_chunks_service.py:84] - Sử dụng vector store ID: vs_681c63f7b8d48191a3394cb3b025b0e7
2025-05-24 13:43:30 - services.document_chunks_service - INFO - [document_chunks_service.py:118] - Successfully saved 10 chunks for file file-5WTRfypFVnSj--dv
2025-05-24 13:43:30 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-5WTRfypFVnSj--dv: 50% - Đang tạo embeddings cho 10 đoạn và lưu vào kho vector vs_681c63f7b8d48191a3394cb3b025b0e7
2025-05-24 13:43:30 - services.document_chunks_service - INFO - [document_chunks_service.py:167] - Creating embeddings for 10 chunks with 5 parallel workers
2025-05-24 13:43:30 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 1 (10 chunks)
2025-05-24 13:43:32 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 13:43:32 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 13:43:32 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 13:43:32 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 13:43:32 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 13:43:33 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 13:43:33 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 13:43:33 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 13:43:33 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 13:43:33 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 13:43:33 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 13:43:33 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 13:43:33 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 13:43:33 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 13:43:35 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 13:43:35 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 13:43:35 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 13:43:36 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 13:43:36 - api.file_endpoints - INFO - [file_endpoints.py:101] - Progress update for file-5WTRfypFVnSj--dv: 100% - Hoàn thành xử lý tập tin với 10/10 đoạn
2025-05-24 13:43:36 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-5WTRfypFVnSj--dv_0c4bdb45-2f6e-4d0c-bbea-00b22fc24220.pdf
2025-05-24 13:48:16 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 13:48:28 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 13:48:28 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 13:48:28 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 13:48:28 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 13:49:27 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 13:49:31 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 13:49:31 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 13:49:32 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 13:49:32 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 13:51:33 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-07fd...
2025-05-24 13:51:33 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-24 13:51:33 - services.s3_file_service - INFO - [s3_file_service.py:130] - Created file record: file-WI5AIy6sWKy3-iQu, 1748068115726-82cfe3f1-10ad-489f-981e-41689244fc55.pdf
2025-05-24 13:51:33 - api.file_endpoints - INFO - [file_endpoints.py:285] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 13:51:33 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-WI5AIy6sWKy3-iQu: 0% - Đã tạo file record, chuẩn bị xử lý
2025-05-24 13:51:33 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.4865s
2025-05-24 13:51:33 - api.file_endpoints - INFO - [file_endpoints.py:135] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 13:51:33 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-WI5AIy6sWKy3-iQu: 5% - Đang tải tập tin từ URL
2025-05-24 13:51:33 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/1748068115726-82cfe3f1-10ad-489f-981e-41689244fc55.pdf
2025-05-24 13:51:33 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-WI5AIy6sWKy3-iQu_fae986b0-be0a-45fb-b035-515a1edd289f.pdf
2025-05-24 13:51:33 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-WI5AIy6sWKy3-iQu_fae986b0-be0a-45fb-b035-515a1edd289f.pdf
2025-05-24 13:51:33 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-WI5AIy6sWKy3-iQu_fae986b0-be0a-45fb-b035-515a1edd289f.pdf
2025-05-24 13:51:33 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-WI5AIy6sWKy3-iQu: 20% - Đang chuyển đổi tập tin sang Markdown
2025-05-24 13:51:33 - services.markdown_service - INFO - [markdown_service.py:71] - Converting file to Markdown: temp-s3\temp_file-WI5AIy6sWKy3-iQu_fae986b0-be0a-45fb-b035-515a1edd289f.pdf (type: .pdf)
2025-05-24 13:51:33 - services.markdown_service - INFO - [markdown_service.py:138] - Converting PDF to Markdown using OCR
2025-05-24 13:51:33 - services.image_processing_service - INFO - [image_processing_service.py:329] - Processing PDF with OCR: temp-s3\temp_file-WI5AIy6sWKy3-iQu_fae986b0-be0a-45fb-b035-515a1edd289f.pdf
2025-05-24 13:51:33 - services.image_processing_service - INFO - [image_processing_service.py:60] - Converting PDF to images: temp-s3\temp_file-WI5AIy6sWKy3-iQu_fae986b0-be0a-45fb-b035-515a1edd289f.pdf (DPI: 300)
2025-05-24 13:51:33 - services.image_processing_service - WARNING - [image_processing_service.py:35] - PDF validation failed: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 13:51:33 - services.image_processing_service - WARNING - [image_processing_service.py:64] - PDF validation failed, attempting conversion anyway
2025-05-24 13:51:33 - services.image_processing_service - WARNING - [image_processing_service.py:88] - Standard PDF conversion failed: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 13:51:33 - services.image_processing_service - INFO - [image_processing_service.py:92] - Trying with lower DPI (150)
2025-05-24 13:51:33 - services.image_processing_service - WARNING - [image_processing_service.py:101] - Lower DPI conversion failed: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 13:51:33 - services.image_processing_service - INFO - [image_processing_service.py:105] - Trying basic conversion
2025-05-24 13:51:33 - services.image_processing_service - ERROR - [image_processing_service.py:115] - All PDF conversion methods failed: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 13:51:33 - services.image_processing_service - ERROR - [image_processing_service.py:150] - Error converting PDF to images: Could not convert PDF to images. Last error: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 13:51:33 - services.image_processing_service - ERROR - [image_processing_service.py:368] - Error processing PDF with OCR: Could not convert PDF to images. Last error: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 13:51:33 - services.markdown_service - WARNING - [markdown_service.py:154] - OCR failed: Could not convert PDF to images. Last error: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table

, falling back to markitdown
2025-05-24 13:51:33 - services.markdown_service - INFO - [markdown_service.py:157] - Converting PDF to Markdown using markitdown
2025-05-24 13:51:34 - services.markdown_service - INFO - [markdown_service.py:126] - Successfully converted file to Markdown (15919 characters)
2025-05-24 13:51:34 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-WI5AIy6sWKy3-iQu: 40% - Đang chia nhỏ nội dung
2025-05-24 13:51:34 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-24 13:51:34 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 10 well-sized chunks
2025-05-24 13:51:34 - services.document_chunks_service - INFO - [document_chunks_service.py:67] - Split content into 10 chunks for file file-WI5AIy6sWKy3-iQu
2025-05-24 13:51:34 - services.document_chunks_service - INFO - [document_chunks_service.py:84] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 13:51:34 - services.document_chunks_service - INFO - [document_chunks_service.py:118] - Successfully saved 10 chunks for file file-WI5AIy6sWKy3-iQu
2025-05-24 13:51:34 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-WI5AIy6sWKy3-iQu: 60% - Đang tạo embeddings cho 10 đoạn và lưu vào kho vector vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 13:51:34 - services.document_chunks_service - INFO - [document_chunks_service.py:167] - Creating embeddings for 10 chunks with 5 parallel workers
2025-05-24 13:51:34 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 1 (10 chunks)
2025-05-24 13:51:36 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 13:51:36 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 13:51:37 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 13:51:37 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 13:51:37 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 13:51:37 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 13:51:37 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 13:51:37 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 13:51:37 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 13:51:37 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 13:51:38 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 13:51:38 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 13:51:38 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 13:51:38 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 13:51:39 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 13:51:39 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 13:51:39 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 13:51:39 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 13:51:40 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-WI5AIy6sWKy3-iQu: 100% - Hoàn thành xử lý tập tin với 10/10 đoạn
2025-05-24 13:51:40 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-WI5AIy6sWKy3-iQu_fae986b0-be0a-45fb-b035-515a1edd289f.pdf
2025-05-24 13:54:28 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 13:54:35 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 13:54:35 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 13:54:35 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 13:54:35 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 13:54:47 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-test...
2025-05-24 13:54:47 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-test... (length: 14)
2025-05-24 13:54:48 - services.api_key_service - WARNING - [api_key_service.py:99] - API key not found or not active: redai-test...
2025-05-24 13:54:48 - api.middleware.api_key_auth - WARNING - [api_key_auth.py:121] - Invalid API key attempt from 127.0.0.1: redai-test...
2025-05-24 13:54:48 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.0155s
2025-05-24 13:55:02 - main - INFO - [main.py:176] - Request to / completed in 0.0008s
2025-05-24 13:55:21 - services.api_key_service - INFO - [api_key_service.py:38] - Created new API key: redai-c4dd...
2025-05-24 13:55:21 - main - INFO - [main.py:176] - Request to /api/auth/keys completed in 0.0874s
2025-05-24 13:55:23 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-c4dd...
2025-05-24 13:55:23 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-c4dd... (length: 42)
2025-05-24 13:55:23 - services.s3_file_service - INFO - [s3_file_service.py:250] - Created file record fast: file-2by0pFmDeEq5ZHFZ, 34af6a9f-47c7-4012-8c78-4099b719689b-Example.pdf
2025-05-24 13:55:23 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-2by0pFmDeEq5ZHFZ: 0% - Đã tạo file record, chuẩn bị xử lý
2025-05-24 13:55:23 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.0928s
2025-05-24 13:55:23 - api.file_endpoints - INFO - [file_endpoints.py:135] - Sử dụng vector store ID: vs_681c63f7b8d48191a3394cb3b025b0e7
2025-05-24 13:55:23 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-2by0pFmDeEq5ZHFZ: 5% - Đang kiểm tra URL
2025-05-24 13:55:23 - services.s3_file_service - WARNING - [s3_file_service.py:58] - URL not accessible: https://cdn.redai.vn/knowledge_files/4/34af6a9f-47c7-4012-8c78-4099b719689b-Example.pdf, status code: 404
2025-05-24 13:55:23 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-2by0pFmDeEq5ZHFZ: 100% - Lỗi: URL không hợp lệ hoặc không thể truy cập
2025-05-24 13:57:30 - services.api_key_service - INFO - [api_key_service.py:38] - Created new API key: redai-641f...
2025-05-24 13:57:30 - main - INFO - [main.py:176] - Request to /api/auth/keys completed in 0.0124s
2025-05-24 13:57:32 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-641f...
2025-05-24 13:57:32 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-641f... (length: 42)
2025-05-24 13:57:32 - services.s3_file_service - INFO - [s3_file_service.py:250] - Created file record fast: file-qgYWrott63xGUhX5, 34af6a9f-47c7-4012-8c78-4099b719689b-Example.pdf
2025-05-24 13:57:32 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-qgYWrott63xGUhX5: 0% - Đã tạo file record, chuẩn bị xử lý
2025-05-24 13:57:32 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.0156s
2025-05-24 13:57:32 - api.file_endpoints - INFO - [file_endpoints.py:135] - Sử dụng vector store ID: vs_681c63f7b8d48191a3394cb3b025b0e7
2025-05-24 13:57:32 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-qgYWrott63xGUhX5: 5% - Đang kiểm tra URL
2025-05-24 13:57:32 - services.s3_file_service - WARNING - [s3_file_service.py:58] - URL not accessible: https://cdn.redai.vn/knowledge_files/4/34af6a9f-47c7-4012-8c78-4099b719689b-Example.pdf, status code: 404
2025-05-24 13:57:32 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-qgYWrott63xGUhX5: 100% - Lỗi: URL không hợp lệ hoặc không thể truy cập
2025-05-24 13:57:34 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/file-qgYWrott63xGUhX5/progress with API key: redai-641f...
2025-05-24 13:57:34 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-641f... (length: 42)
2025-05-24 13:57:34 - main - INFO - [main.py:176] - Request to /api/files/file-qgYWrott63xGUhX5/progress completed in 0.0061s
2025-05-24 13:57:58 - main - INFO - [main.py:176] - Request to / completed in 0.0004s
2025-05-24 13:58:48 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 13:58:53 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 13:58:53 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 13:58:53 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 13:58:53 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 14:00:08 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-07fd...
2025-05-24 14:00:08 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-24 14:00:08 - services.s3_file_service - INFO - [s3_file_service.py:250] - Created file record fast: file-LfieZCxcimT0vb6K, 1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 14:00:08 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-LfieZCxcimT0vb6K: 0% - Đã tạo file record, chuẩn bị xử lý
2025-05-24 14:00:08 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.2976s
2025-05-24 14:00:08 - api.file_endpoints - INFO - [file_endpoints.py:135] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:00:08 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-LfieZCxcimT0vb6K: 5% - Đang kiểm tra URL
2025-05-24 14:00:08 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-LfieZCxcimT0vb6K: 10% - Đang tải tập tin từ URL
2025-05-24 14:00:08 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 14:00:08 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-LfieZCxcimT0vb6K_7bb59f41-d8c3-4d25-894b-9d337afdf98b.docx
2025-05-24 14:00:09 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-LfieZCxcimT0vb6K_7bb59f41-d8c3-4d25-894b-9d337afdf98b.docx
2025-05-24 14:00:09 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-LfieZCxcimT0vb6K_7bb59f41-d8c3-4d25-894b-9d337afdf98b.docx
2025-05-24 14:00:09 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-LfieZCxcimT0vb6K: 30% - Đang chuyển đổi tập tin sang Markdown
2025-05-24 14:00:09 - services.markdown_service - INFO - [markdown_service.py:71] - Converting file to Markdown: temp-s3\temp_file-LfieZCxcimT0vb6K_7bb59f41-d8c3-4d25-894b-9d337afdf98b.docx (type: .docx)
2025-05-24 14:00:09 - services.markdown_service - INFO - [markdown_service.py:200] - Converting Word document to Markdown
2025-05-24 14:00:13 - services.markdown_service - INFO - [markdown_service.py:126] - Successfully converted file to Markdown (59436 characters)
2025-05-24 14:00:13 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-LfieZCxcimT0vb6K: 50% - Đang chia nhỏ nội dung
2025-05-24 14:00:13 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-24 14:00:13 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 35 well-sized chunks
2025-05-24 14:00:13 - services.document_chunks_service - INFO - [document_chunks_service.py:67] - Split content into 35 chunks for file file-LfieZCxcimT0vb6K
2025-05-24 14:00:13 - services.document_chunks_service - INFO - [document_chunks_service.py:84] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:00:13 - services.document_chunks_service - INFO - [document_chunks_service.py:118] - Successfully saved 35 chunks for file file-LfieZCxcimT0vb6K
2025-05-24 14:00:13 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-LfieZCxcimT0vb6K: 70% - Đang tạo embeddings cho 35 đoạn và lưu vào kho vector vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:00:13 - services.document_chunks_service - INFO - [document_chunks_service.py:167] - Creating embeddings for 35 chunks with 5 parallel workers
2025-05-24 14:00:13 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 1 (10 chunks)
2025-05-24 14:00:15 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:00:16 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:00:16 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:00:16 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:00:16 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:16 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:17 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:00:17 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:00:17 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:17 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:17 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:17 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:17 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:17 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:18 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:18 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:19 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:19 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:19 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 2 (10 chunks)
2025-05-24 14:00:19 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:19 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:20 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:20 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:20 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:20 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:20 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:20 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:20 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:20 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:21 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:21 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:21 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:21 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:21 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:21 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:21 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:22 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:22 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:22 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:23 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:23 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:23 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:23 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:23 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:23 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:23 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:24 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:24 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:24 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:24 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 3 (10 chunks)
2025-05-24 14:00:25 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:25 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:26 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:26 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:26 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:26 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:26 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:26 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:26 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:26 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:26 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:26 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:26 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:27 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:27 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:27 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:27 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:27 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:27 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:27 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:28 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:28 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:28 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:28 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:28 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:28 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:28 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:29 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:29 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:29 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:30 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 4 (5 chunks)
2025-05-24 14:00:30 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:30 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:31 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:31 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:31 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:31 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:31 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:31 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:31 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:31 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:00:31 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:32 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:32 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:32 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:32 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:00:32 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-LfieZCxcimT0vb6K: 100% - Hoàn thành xử lý tập tin với 35/35 đoạn
2025-05-24 14:00:32 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-LfieZCxcimT0vb6K_7bb59f41-d8c3-4d25-894b-9d337afdf98b.docx
2025-05-24 14:01:45 - services.api_key_service - INFO - [api_key_service.py:38] - Created new API key: redai-7105...
2025-05-24 14:01:45 - main - INFO - [main.py:176] - Request to /api/auth/keys completed in 0.1750s
2025-05-24 14:01:48 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-7105...
2025-05-24 14:01:48 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-7105... (length: 42)
2025-05-24 14:01:48 - services.s3_file_service - INFO - [s3_file_service.py:250] - Created file record fast: file-tOJKCSplMicIVtAM, 34af6a9f-47c7-4012-8c78-4099b719689b-Example.pdf
2025-05-24 14:01:48 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-tOJKCSplMicIVtAM: 0% - Đã tạo file record, chuẩn bị xử lý
2025-05-24 14:01:48 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.1461s
2025-05-24 14:01:48 - api.file_endpoints - INFO - [file_endpoints.py:135] - Sử dụng vector store ID: vs_681c63f7b8d48191a3394cb3b025b0e7
2025-05-24 14:01:48 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-tOJKCSplMicIVtAM: 5% - Đang kiểm tra URL
2025-05-24 14:01:48 - services.s3_file_service - WARNING - [s3_file_service.py:58] - URL not accessible: https://cdn.redai.vn/knowledge_files/4/34af6a9f-47c7-4012-8c78-4099b719689b-Example.pdf, status code: 404
2025-05-24 14:01:48 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-tOJKCSplMicIVtAM: 100% - Lỗi: URL không hợp lệ hoặc không thể truy cập
2025-05-24 14:13:16 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-07fd...
2025-05-24 14:13:16 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-24 14:13:16 - services.s3_file_service - INFO - [s3_file_service.py:250] - Created file record fast: file-vnCDPAJYSq09PjK9, 1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 14:13:16 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-vnCDPAJYSq09PjK9: 0% - Đã tạo file record, chuẩn bị xử lý
2025-05-24 14:13:16 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.2525s
2025-05-24 14:13:16 - api.file_endpoints - INFO - [file_endpoints.py:135] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:13:16 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-vnCDPAJYSq09PjK9: 5% - Đang kiểm tra URL
2025-05-24 14:13:16 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-vnCDPAJYSq09PjK9: 10% - Đang tải tập tin từ URL
2025-05-24 14:13:16 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 14:13:16 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-vnCDPAJYSq09PjK9_19ac79a8-980f-433d-824b-3bea5e17dad1.docx
2025-05-24 14:13:17 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-vnCDPAJYSq09PjK9_19ac79a8-980f-433d-824b-3bea5e17dad1.docx
2025-05-24 14:13:17 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-vnCDPAJYSq09PjK9_19ac79a8-980f-433d-824b-3bea5e17dad1.docx
2025-05-24 14:13:17 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-vnCDPAJYSq09PjK9: 30% - Đang chuyển đổi tập tin sang Markdown
2025-05-24 14:13:17 - services.markdown_service - INFO - [markdown_service.py:71] - Converting file to Markdown: temp-s3\temp_file-vnCDPAJYSq09PjK9_19ac79a8-980f-433d-824b-3bea5e17dad1.docx (type: .docx)
2025-05-24 14:13:17 - services.markdown_service - INFO - [markdown_service.py:200] - Converting Word document to Markdown
2025-05-24 14:13:23 - services.markdown_service - INFO - [markdown_service.py:126] - Successfully converted file to Markdown (59436 characters)
2025-05-24 14:13:23 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-vnCDPAJYSq09PjK9: 50% - Đang chia nhỏ nội dung
2025-05-24 14:13:23 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-24 14:13:23 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 35 well-sized chunks
2025-05-24 14:13:23 - services.document_chunks_service - INFO - [document_chunks_service.py:67] - Split content into 35 chunks for file file-vnCDPAJYSq09PjK9
2025-05-24 14:13:23 - services.document_chunks_service - INFO - [document_chunks_service.py:84] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:13:24 - services.document_chunks_service - INFO - [document_chunks_service.py:118] - Successfully saved 35 chunks for file file-vnCDPAJYSq09PjK9
2025-05-24 14:13:24 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-vnCDPAJYSq09PjK9: 70% - Đang tạo embeddings cho 35 đoạn và lưu vào kho vector vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:13:24 - services.document_chunks_service - INFO - [document_chunks_service.py:167] - Creating embeddings for 35 chunks with 5 parallel workers
2025-05-24 14:13:24 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 1 (10 chunks)
2025-05-24 14:13:25 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:13:26 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:13:26 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:13:26 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:13:26 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:13:26 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:26 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:26 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:13:27 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:27 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:27 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:27 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:27 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:27 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:27 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:28 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:28 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:28 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:28 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 2 (10 chunks)
2025-05-24 14:13:29 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:29 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:30 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:30 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:30 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:30 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:30 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:30 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:30 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:30 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:30 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:30 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:30 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:31 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:31 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:31 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:31 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:31 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:31 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:31 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:32 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:32 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:32 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:32 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:32 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:32 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:32 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:33 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:33 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:33 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:34 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 3 (10 chunks)
2025-05-24 14:13:34 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:34 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:35 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:35 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:35 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:35 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:35 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:35 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:35 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:35 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:35 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:36 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:36 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:36 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:36 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:36 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:36 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:36 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:36 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:37 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:37 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:37 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:37 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:37 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:37 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:37 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:38 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:38 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:38 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:39 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:39 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 4 (5 chunks)
2025-05-24 14:13:39 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:39 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:40 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:40 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:40 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:40 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:40 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:40 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:40 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:40 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:13:40 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:41 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:41 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:41 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:41 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:13:42 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-vnCDPAJYSq09PjK9: 100% - Hoàn thành xử lý tập tin với 35/35 đoạn
2025-05-24 14:13:42 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-vnCDPAJYSq09PjK9_19ac79a8-980f-433d-824b-3bea5e17dad1.docx
2025-05-24 14:16:26 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 14:16:33 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 14:16:33 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 14:16:33 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 14:16:33 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 14:21:09 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-07fd...
2025-05-24 14:21:09 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-24 14:21:09 - services.s3_file_service - INFO - [s3_file_service.py:250] - Created file record fast: file-BGBfBWJ-drgcMrLC, 1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 14:21:09 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-BGBfBWJ-drgcMrLC: 0% - Đã tạo file record, chuẩn bị xử lý
2025-05-24 14:21:09 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.3319s
2025-05-24 14:21:09 - api.file_endpoints - INFO - [file_endpoints.py:135] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:21:09 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-BGBfBWJ-drgcMrLC: 5% - Đang kiểm tra URL
2025-05-24 14:21:10 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-BGBfBWJ-drgcMrLC: 10% - Đang tải tập tin từ URL
2025-05-24 14:21:10 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 14:21:10 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-BGBfBWJ-drgcMrLC_162e3d88-ea7d-4f6d-b321-5dcd3c5370a6.docx
2025-05-24 14:21:10 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-BGBfBWJ-drgcMrLC_162e3d88-ea7d-4f6d-b321-5dcd3c5370a6.docx
2025-05-24 14:21:10 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-BGBfBWJ-drgcMrLC_162e3d88-ea7d-4f6d-b321-5dcd3c5370a6.docx
2025-05-24 14:21:10 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-BGBfBWJ-drgcMrLC: 30% - Đang chuyển đổi tập tin sang Markdown
2025-05-24 14:21:10 - services.markdown_service - INFO - [markdown_service.py:71] - Converting file to Markdown: temp-s3\temp_file-BGBfBWJ-drgcMrLC_162e3d88-ea7d-4f6d-b321-5dcd3c5370a6.docx (type: .docx)
2025-05-24 14:21:10 - services.markdown_service - INFO - [markdown_service.py:200] - Converting Word document to Markdown
2025-05-24 14:21:16 - services.markdown_service - INFO - [markdown_service.py:126] - Successfully converted file to Markdown (59436 characters)
2025-05-24 14:21:16 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-BGBfBWJ-drgcMrLC: 50% - Đang chia nhỏ nội dung
2025-05-24 14:21:16 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-24 14:21:16 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 35 well-sized chunks
2025-05-24 14:21:16 - services.document_chunks_service - INFO - [document_chunks_service.py:67] - Split content into 35 chunks for file file-BGBfBWJ-drgcMrLC
2025-05-24 14:21:16 - services.document_chunks_service - INFO - [document_chunks_service.py:84] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:21:16 - services.document_chunks_service - INFO - [document_chunks_service.py:118] - Successfully saved 35 chunks for file file-BGBfBWJ-drgcMrLC
2025-05-24 14:21:16 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-BGBfBWJ-drgcMrLC: 70% - Đang tạo embeddings cho 35 đoạn và lưu vào kho vector vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:21:16 - services.document_chunks_service - INFO - [document_chunks_service.py:167] - Creating embeddings for 35 chunks with 5 parallel workers
2025-05-24 14:21:16 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 1 (10 chunks)
2025-05-24 14:21:19 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:21:20 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:21:20 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:21:20 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:21:20 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:21:20 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:20 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:20 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:21:21 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:21 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:21 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:21 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:21 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:21 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:22 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:22 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:22 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:23 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:23 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 2 (10 chunks)
2025-05-24 14:21:23 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:23 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:24 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:24 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:25 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:25 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:21:25 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:21:25 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:21:25 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:26 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:21:26 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:26 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:26 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:26 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:26 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:21:26 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:21:27 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:27 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:28 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 3 (10 chunks)
2025-05-24 14:21:29 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:29 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:29 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:29 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:29 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:29 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:29 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:29 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:29 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:29 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:30 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:30 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:30 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:30 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:30 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:30 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:30 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:31 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:31 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:31 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:31 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:32 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:32 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:32 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:32 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:32 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:32 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:32 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:33 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:33 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:33 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 4 (5 chunks)
2025-05-24 14:21:34 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:34 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:34 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:34 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:34 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:34 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:34 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:34 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:34 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:34 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:21:35 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:35 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:36 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:36 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:36 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:21:36 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-BGBfBWJ-drgcMrLC: 100% - Hoàn thành xử lý tập tin với 35/35 đoạn
2025-05-24 14:21:36 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-BGBfBWJ-drgcMrLC_162e3d88-ea7d-4f6d-b321-5dcd3c5370a6.docx
2025-05-24 14:27:10 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 14:27:15 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 14:27:15 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 14:27:15 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 14:27:15 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 14:27:24 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-0648...
2025-05-24 14:27:24 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-0648... (length: 38)
2025-05-24 14:27:24 - services.s3_file_service - INFO - [s3_file_service.py:250] - Created file record fast: file-QzvdCB4wuXh-x53s, 1748068115726-82cfe3f1-10ad-489f-981e-41689244fc55.pdf
2025-05-24 14:27:24 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-QzvdCB4wuXh-x53s: 0% - Đã tạo file record, chuẩn bị xử lý
2025-05-24 14:27:24 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.0682s
2025-05-24 14:27:24 - api.file_endpoints - INFO - [file_endpoints.py:135] - Sử dụng vector store ID: vs_681c63f7b8d48191a3394cb3b025b0e7
2025-05-24 14:27:24 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-QzvdCB4wuXh-x53s: 5% - Đang kiểm tra URL
2025-05-24 14:27:24 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-QzvdCB4wuXh-x53s: 10% - Đang tải tập tin từ URL
2025-05-24 14:27:24 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/1748068115726-82cfe3f1-10ad-489f-981e-41689244fc55.pdf
2025-05-24 14:27:24 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-QzvdCB4wuXh-x53s_3db56a03-a99f-4eb8-97e6-d1b3903a6de4.pdf
2025-05-24 14:27:25 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-QzvdCB4wuXh-x53s_3db56a03-a99f-4eb8-97e6-d1b3903a6de4.pdf
2025-05-24 14:27:25 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-QzvdCB4wuXh-x53s_3db56a03-a99f-4eb8-97e6-d1b3903a6de4.pdf
2025-05-24 14:27:25 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-QzvdCB4wuXh-x53s: 30% - Đang chuyển đổi tập tin sang Markdown
2025-05-24 14:27:25 - services.markdown_service - INFO - [markdown_service.py:71] - Converting file to Markdown: temp-s3\temp_file-QzvdCB4wuXh-x53s_3db56a03-a99f-4eb8-97e6-d1b3903a6de4.pdf (type: .pdf)
2025-05-24 14:27:25 - services.markdown_service - INFO - [markdown_service.py:138] - Converting PDF to Markdown using OCR
2025-05-24 14:27:25 - services.image_processing_service - INFO - [image_processing_service.py:329] - Processing PDF with OCR: temp-s3\temp_file-QzvdCB4wuXh-x53s_3db56a03-a99f-4eb8-97e6-d1b3903a6de4.pdf
2025-05-24 14:27:25 - services.image_processing_service - INFO - [image_processing_service.py:60] - Converting PDF to images: temp-s3\temp_file-QzvdCB4wuXh-x53s_3db56a03-a99f-4eb8-97e6-d1b3903a6de4.pdf (DPI: 300)
2025-05-24 14:27:25 - services.image_processing_service - WARNING - [image_processing_service.py:35] - PDF validation failed: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 14:27:25 - services.image_processing_service - WARNING - [image_processing_service.py:64] - PDF validation failed, attempting conversion anyway
2025-05-24 14:27:25 - services.image_processing_service - WARNING - [image_processing_service.py:88] - Standard PDF conversion failed: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 14:27:25 - services.image_processing_service - INFO - [image_processing_service.py:92] - Trying with lower DPI (150)
2025-05-24 14:27:25 - services.image_processing_service - WARNING - [image_processing_service.py:101] - Lower DPI conversion failed: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 14:27:25 - services.image_processing_service - INFO - [image_processing_service.py:105] - Trying basic conversion
2025-05-24 14:27:25 - services.image_processing_service - ERROR - [image_processing_service.py:115] - All PDF conversion methods failed: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 14:27:25 - services.image_processing_service - ERROR - [image_processing_service.py:150] - Error converting PDF to images: Could not convert PDF to images. Last error: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 14:27:25 - services.image_processing_service - ERROR - [image_processing_service.py:368] - Error processing PDF with OCR: Could not convert PDF to images. Last error: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table


2025-05-24 14:27:25 - services.markdown_service - WARNING - [markdown_service.py:154] - OCR failed: Could not convert PDF to images. Last error: Unable to get page count.
Syntax Warning: May not be a PDF file (continuing anyway)

Syntax Error (17): Illegal character '}'

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't find trailer dictionary

Syntax Error: Couldn't read xref table

, falling back to markitdown
2025-05-24 14:27:25 - services.markdown_service - INFO - [markdown_service.py:157] - Converting PDF to Markdown using markitdown
2025-05-24 14:27:25 - services.markdown_service - INFO - [markdown_service.py:126] - Successfully converted file to Markdown (15919 characters)
2025-05-24 14:27:25 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-QzvdCB4wuXh-x53s: 50% - Đang chia nhỏ nội dung
2025-05-24 14:27:25 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-24 14:27:25 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 10 well-sized chunks
2025-05-24 14:27:25 - services.document_chunks_service - INFO - [document_chunks_service.py:67] - Split content into 10 chunks for file file-QzvdCB4wuXh-x53s
2025-05-24 14:27:25 - services.document_chunks_service - INFO - [document_chunks_service.py:84] - Sử dụng vector store ID: vs_681c63f7b8d48191a3394cb3b025b0e7
2025-05-24 14:27:25 - services.document_chunks_service - INFO - [document_chunks_service.py:118] - Successfully saved 10 chunks for file file-QzvdCB4wuXh-x53s
2025-05-24 14:27:25 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-QzvdCB4wuXh-x53s: 70% - Đang tạo embeddings cho 10 đoạn và lưu vào kho vector vs_681c63f7b8d48191a3394cb3b025b0e7
2025-05-24 14:27:25 - services.document_chunks_service - INFO - [document_chunks_service.py:167] - Creating embeddings for 10 chunks with 5 parallel workers
2025-05-24 14:27:25 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 1 (10 chunks)
2025-05-24 14:27:27 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:27:27 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:27:28 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:27:28 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:27:28 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:27:28 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:27:28 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:27:28 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:27:28 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:27:28 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:27:29 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:27:29 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:27:29 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:27:29 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:27:30 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:27:30 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:27:30 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:27:30 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:27:31 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-QzvdCB4wuXh-x53s: 100% - Hoàn thành xử lý tập tin với 10/10 đoạn
2025-05-24 14:27:31 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-QzvdCB4wuXh-x53s_3db56a03-a99f-4eb8-97e6-d1b3903a6de4.pdf
2025-05-24 14:28:23 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 14:28:35 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 14:28:35 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 14:28:35 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 14:28:35 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 14:29:37 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-07fd...
2025-05-24 14:29:37 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-24 14:29:37 - services.s3_file_service - INFO - [s3_file_service.py:250] - Created file record fast: file-_zJAIP7GcYdaEtkD, 1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 14:29:37 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-_zJAIP7GcYdaEtkD: 0% - Đã tạo file record, chuẩn bị xử lý
2025-05-24 14:29:37 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.0686s
2025-05-24 14:29:37 - api.file_endpoints - INFO - [file_endpoints.py:135] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:29:37 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-_zJAIP7GcYdaEtkD: 5% - Đang kiểm tra URL
2025-05-24 14:29:37 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-_zJAIP7GcYdaEtkD: 10% - Đang tải tập tin từ URL
2025-05-24 14:29:37 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 14:29:37 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-_zJAIP7GcYdaEtkD_614ec03f-28d2-4506-a214-d51050bdbb84.docx
2025-05-24 14:29:37 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-_zJAIP7GcYdaEtkD_614ec03f-28d2-4506-a214-d51050bdbb84.docx
2025-05-24 14:29:37 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-_zJAIP7GcYdaEtkD_614ec03f-28d2-4506-a214-d51050bdbb84.docx
2025-05-24 14:29:37 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-_zJAIP7GcYdaEtkD: 30% - Đang chuyển đổi tập tin sang Markdown
2025-05-24 14:29:37 - services.markdown_service - INFO - [markdown_service.py:71] - Converting file to Markdown: temp-s3\temp_file-_zJAIP7GcYdaEtkD_614ec03f-28d2-4506-a214-d51050bdbb84.docx (type: .docx)
2025-05-24 14:29:38 - services.markdown_service - INFO - [markdown_service.py:200] - Converting Word document to Markdown
2025-05-24 14:29:43 - services.markdown_service - INFO - [markdown_service.py:126] - Successfully converted file to Markdown (59436 characters)
2025-05-24 14:29:43 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-_zJAIP7GcYdaEtkD: 50% - Đang chia nhỏ nội dung
2025-05-24 14:29:43 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-24 14:29:43 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 35 well-sized chunks
2025-05-24 14:29:43 - services.document_chunks_service - INFO - [document_chunks_service.py:67] - Split content into 35 chunks for file file-_zJAIP7GcYdaEtkD
2025-05-24 14:29:43 - services.document_chunks_service - INFO - [document_chunks_service.py:84] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:29:44 - services.document_chunks_service - INFO - [document_chunks_service.py:118] - Successfully saved 35 chunks for file file-_zJAIP7GcYdaEtkD
2025-05-24 14:29:44 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-_zJAIP7GcYdaEtkD: 70% - Đang tạo embeddings cho 35 đoạn và lưu vào kho vector vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:29:44 - services.document_chunks_service - INFO - [document_chunks_service.py:167] - Creating embeddings for 35 chunks with 5 parallel workers
2025-05-24 14:29:44 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 1 (10 chunks)
2025-05-24 14:29:45 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:29:45 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:29:45 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:29:46 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:29:46 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:29:46 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:29:46 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:46 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:46 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:46 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:46 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:46 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:47 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:47 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:47 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:48 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:48 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:48 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:49 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 2 (10 chunks)
2025-05-24 14:29:49 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:49 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:50 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:50 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:50 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:50 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:50 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:50 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:50 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:50 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:50 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:51 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:51 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:51 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:51 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:51 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:51 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:52 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:52 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:52 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:52 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:52 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:52 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:52 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:52 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:52 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:53 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:53 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:53 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:54 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:54 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 3 (10 chunks)
2025-05-24 14:29:54 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:54 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:55 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:56 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:56 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:57 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:57 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:57 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:57 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:57 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:58 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:58 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:58 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:58 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:59 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:29:59 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 4 (5 chunks)
2025-05-24 14:29:59 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:29:59 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:30:00 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:30:00 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:30:00 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:30:00 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:30:00 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:30:00 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:30:00 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:30:00 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:30:01 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:30:01 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:30:01 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:30:02 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:30:02 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:30:02 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-_zJAIP7GcYdaEtkD: 100% - Hoàn thành xử lý tập tin với 35/35 đoạn
2025-05-24 14:30:02 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-_zJAIP7GcYdaEtkD_614ec03f-28d2-4506-a214-d51050bdbb84.docx
2025-05-24 14:34:43 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-07fd...
2025-05-24 14:34:43 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-24 14:34:43 - services.s3_file_service - INFO - [s3_file_service.py:250] - Created file record fast: file-6qNK_uhvV3RC9S1C, 1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 14:34:43 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-6qNK_uhvV3RC9S1C: 0% - Đã tạo file record, chuẩn bị xử lý
2025-05-24 14:34:43 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.2969s
2025-05-24 14:34:43 - api.file_endpoints - INFO - [file_endpoints.py:135] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:34:43 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-6qNK_uhvV3RC9S1C: 5% - Đang kiểm tra URL
2025-05-24 14:34:43 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-6qNK_uhvV3RC9S1C: 10% - Đang tải tập tin từ URL
2025-05-24 14:34:43 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 14:34:43 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-6qNK_uhvV3RC9S1C_1457ed15-53ac-46b9-b220-b6d73ed6c71f.docx
2025-05-24 14:34:44 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-6qNK_uhvV3RC9S1C_1457ed15-53ac-46b9-b220-b6d73ed6c71f.docx
2025-05-24 14:34:44 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-6qNK_uhvV3RC9S1C_1457ed15-53ac-46b9-b220-b6d73ed6c71f.docx
2025-05-24 14:34:44 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-6qNK_uhvV3RC9S1C: 30% - Đang chuyển đổi tập tin sang Markdown
2025-05-24 14:34:44 - services.markdown_service - INFO - [markdown_service.py:71] - Converting file to Markdown: temp-s3\temp_file-6qNK_uhvV3RC9S1C_1457ed15-53ac-46b9-b220-b6d73ed6c71f.docx (type: .docx)
2025-05-24 14:34:44 - services.markdown_service - INFO - [markdown_service.py:200] - Converting Word document to Markdown
2025-05-24 14:34:49 - services.markdown_service - INFO - [markdown_service.py:126] - Successfully converted file to Markdown (59436 characters)
2025-05-24 14:34:49 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-6qNK_uhvV3RC9S1C: 50% - Đang chia nhỏ nội dung
2025-05-24 14:34:49 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-24 14:34:49 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 35 well-sized chunks
2025-05-24 14:34:49 - services.document_chunks_service - INFO - [document_chunks_service.py:67] - Split content into 35 chunks for file file-6qNK_uhvV3RC9S1C
2025-05-24 14:34:49 - services.document_chunks_service - INFO - [document_chunks_service.py:84] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:34:49 - services.document_chunks_service - INFO - [document_chunks_service.py:118] - Successfully saved 35 chunks for file file-6qNK_uhvV3RC9S1C
2025-05-24 14:34:49 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-6qNK_uhvV3RC9S1C: 70% - Đang tạo embeddings cho 35 đoạn và lưu vào kho vector vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:34:49 - services.document_chunks_service - INFO - [document_chunks_service.py:167] - Creating embeddings for 35 chunks with 5 parallel workers
2025-05-24 14:34:49 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 1 (10 chunks)
2025-05-24 14:34:50 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:34:51 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:34:51 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:34:51 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:34:51 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:51 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:51 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:34:51 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:34:52 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:52 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:52 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:52 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:52 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:52 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:53 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:34:53 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:34:54 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:34:54 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:34:54 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 2 (10 chunks)
2025-05-24 14:34:54 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:54 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:55 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:34:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:34:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:34:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:34:57 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:34:57 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:34:57 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:57 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:58 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:58 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:58 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:58 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:58 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:58 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:34:58 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:34:59 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:34:59 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:34:59 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:34:59 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 3 (10 chunks)
2025-05-24 14:35:00 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:00 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:01 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:01 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:01 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:01 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:01 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:01 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:01 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:01 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:01 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:35:01 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:01 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:02 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:35:02 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:35:02 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:35:02 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:35:02 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:02 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:03 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:35:03 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:35:03 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:03 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:03 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:03 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:03 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:03 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:04 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:35:04 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:35:04 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:35:05 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 4 (5 chunks)
2025-05-24 14:35:05 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:05 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:06 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:06 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:06 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:06 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:06 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:06 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:06 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:06 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:35:06 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:35:07 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:35:07 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:35:07 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:35:07 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:35:08 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-6qNK_uhvV3RC9S1C: 100% - Hoàn thành xử lý tập tin với 35/35 đoạn
2025-05-24 14:35:08 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-6qNK_uhvV3RC9S1C_1457ed15-53ac-46b9-b220-b6d73ed6c71f.docx
2025-05-24 14:43:19 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 14:43:28 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 14:43:28 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 14:43:28 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 14:43:28 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 14:43:34 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-07fd...
2025-05-24 14:43:34 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-24 14:43:35 - services.s3_file_service - INFO - [s3_file_service.py:250] - Created file record fast: file-bNXR76rFcn49VZ4I, 1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 14:43:35 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-bNXR76rFcn49VZ4I: 0% - Đã tạo file record, chuẩn bị xử lý
2025-05-24 14:43:35 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.0698s
2025-05-24 14:43:35 - api.file_endpoints - INFO - [file_endpoints.py:135] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:43:35 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-bNXR76rFcn49VZ4I: 5% - Đang kiểm tra URL
2025-05-24 14:43:35 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-bNXR76rFcn49VZ4I: 10% - Đang tải tập tin từ URL
2025-05-24 14:43:35 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 14:43:35 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-bNXR76rFcn49VZ4I_8072a04f-3649-4836-9e47-62d1fa436a20.docx
2025-05-24 14:43:35 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-bNXR76rFcn49VZ4I_8072a04f-3649-4836-9e47-62d1fa436a20.docx
2025-05-24 14:43:35 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-bNXR76rFcn49VZ4I_8072a04f-3649-4836-9e47-62d1fa436a20.docx
2025-05-24 14:43:35 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-bNXR76rFcn49VZ4I: 30% - Đang chuyển đổi tập tin sang Markdown
2025-05-24 14:43:35 - services.markdown_service - INFO - [markdown_service.py:71] - Converting file to Markdown: temp-s3\temp_file-bNXR76rFcn49VZ4I_8072a04f-3649-4836-9e47-62d1fa436a20.docx (type: .docx)
2025-05-24 14:43:35 - services.markdown_service - INFO - [markdown_service.py:200] - Converting Word document to Markdown
2025-05-24 14:43:41 - services.markdown_service - INFO - [markdown_service.py:126] - Successfully converted file to Markdown (59436 characters)
2025-05-24 14:43:41 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-bNXR76rFcn49VZ4I: 50% - Đang chia nhỏ nội dung
2025-05-24 14:43:41 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-24 14:43:41 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 35 well-sized chunks
2025-05-24 14:43:41 - services.document_chunks_service - INFO - [document_chunks_service.py:67] - Split content into 35 chunks for file file-bNXR76rFcn49VZ4I
2025-05-24 14:43:41 - services.document_chunks_service - INFO - [document_chunks_service.py:84] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:43:41 - services.document_chunks_service - INFO - [document_chunks_service.py:118] - Successfully saved 35 chunks for file file-bNXR76rFcn49VZ4I
2025-05-24 14:43:41 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-bNXR76rFcn49VZ4I: 70% - Đang tạo embeddings cho 35 đoạn và lưu vào kho vector vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:43:41 - services.document_chunks_service - INFO - [document_chunks_service.py:167] - Creating embeddings for 35 chunks with 5 parallel workers
2025-05-24 14:43:41 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 1 (10 chunks)
2025-05-24 14:43:43 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:43:44 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:43:44 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:43:44 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:43:44 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:43:44 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:43:45 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:45 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:45 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:45 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:45 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:45 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:45 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:45 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:46 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:47 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:47 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:47 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:47 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 2 (10 chunks)
2025-05-24 14:43:47 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:47 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:50 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:50 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:50 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:50 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:50 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:50 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:50 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:50 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:50 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:50 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:50 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:51 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:51 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:51 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:51 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:51 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:51 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:51 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:52 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:52 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:52 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:52 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:52 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:52 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:52 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:53 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:53 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:53 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:54 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 3 (10 chunks)
2025-05-24 14:43:54 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:54 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:55 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:56 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:56 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:56 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:56 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:57 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:57 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:57 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:57 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:58 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:58 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:58 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:58 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:43:59 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 4 (5 chunks)
2025-05-24 14:43:59 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:59 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:59 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:59 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:59 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:43:59 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:44:00 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:44:00 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:44:00 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:44:00 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:44:00 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:44:00 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:44:01 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:44:01 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:44:01 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:44:01 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-bNXR76rFcn49VZ4I: 100% - Hoàn thành xử lý tập tin với 35/35 đoạn
2025-05-24 14:44:01 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-bNXR76rFcn49VZ4I_8072a04f-3649-4836-9e47-62d1fa436a20.docx
2025-05-24 14:48:47 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 14:48:56 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 14:48:56 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 14:48:56 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 14:48:56 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 14:49:09 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-07fd...
2025-05-24 14:49:09 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-24 14:49:09 - services.s3_file_service - INFO - [s3_file_service.py:250] - Created file record fast: file-E9AxlvewhkHXPJwX, 1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 14:49:09 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-E9AxlvewhkHXPJwX: 0% - Đã tạo file record, chuẩn bị xử lý
2025-05-24 14:49:09 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.0693s
2025-05-24 14:49:09 - api.file_endpoints - INFO - [file_endpoints.py:135] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:49:09 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-E9AxlvewhkHXPJwX: 5% - Đang kiểm tra URL
2025-05-24 14:49:11 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-E9AxlvewhkHXPJwX: 10% - Đang tải tập tin từ URL
2025-05-24 14:49:11 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 14:49:11 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-E9AxlvewhkHXPJwX_caf4232b-81dc-4f17-9e04-026da2814acf.docx
2025-05-24 14:49:12 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-E9AxlvewhkHXPJwX_caf4232b-81dc-4f17-9e04-026da2814acf.docx
2025-05-24 14:49:12 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-E9AxlvewhkHXPJwX_caf4232b-81dc-4f17-9e04-026da2814acf.docx
2025-05-24 14:49:12 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-E9AxlvewhkHXPJwX: 30% - Đang chuyển đổi tập tin sang Markdown
2025-05-24 14:49:12 - services.markdown_service - INFO - [markdown_service.py:71] - Converting file to Markdown: temp-s3\temp_file-E9AxlvewhkHXPJwX_caf4232b-81dc-4f17-9e04-026da2814acf.docx (type: .docx)
2025-05-24 14:49:12 - services.markdown_service - INFO - [markdown_service.py:200] - Converting Word document to Markdown
2025-05-24 14:49:15 - services.markdown_service - INFO - [markdown_service.py:126] - Successfully converted file to Markdown (59436 characters)
2025-05-24 14:49:15 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-E9AxlvewhkHXPJwX: 50% - Đang chia nhỏ nội dung
2025-05-24 14:49:15 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-24 14:49:15 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 35 well-sized chunks
2025-05-24 14:49:15 - services.document_chunks_service - INFO - [document_chunks_service.py:67] - Split content into 35 chunks for file file-E9AxlvewhkHXPJwX
2025-05-24 14:49:15 - services.document_chunks_service - INFO - [document_chunks_service.py:84] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:49:16 - services.document_chunks_service - INFO - [document_chunks_service.py:118] - Successfully saved 35 chunks for file file-E9AxlvewhkHXPJwX
2025-05-24 14:49:16 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-E9AxlvewhkHXPJwX: 70% - Đang tạo embeddings cho 35 đoạn và lưu vào kho vector vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:49:16 - services.document_chunks_service - INFO - [document_chunks_service.py:167] - Creating embeddings for 35 chunks with 5 parallel workers
2025-05-24 14:49:16 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 1 (10 chunks)
2025-05-24 14:49:17 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:49:18 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:49:18 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:49:18 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:49:18 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:18 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:49:18 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:49:18 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:19 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:19 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:19 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:19 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:19 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:19 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:20 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:20 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:20 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:21 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:21 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 2 (10 chunks)
2025-05-24 14:49:21 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:21 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:22 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:22 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:22 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:22 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:22 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:22 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:22 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:22 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:23 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:23 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:23 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:24 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:24 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:24 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:24 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:24 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:24 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:24 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:25 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:25 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:25 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:25 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:25 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:25 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:25 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:26 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:26 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:26 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:27 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 3 (10 chunks)
2025-05-24 14:49:27 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:27 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:28 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:28 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:28 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:28 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:28 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:28 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:28 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:28 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:28 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:28 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:28 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:29 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:29 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:29 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:29 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:29 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:29 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:29 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:30 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:30 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:30 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:30 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:30 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:30 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:31 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:32 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:32 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:32 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:32 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 4 (5 chunks)
2025-05-24 14:49:33 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:33 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:33 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:33 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:33 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:33 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:33 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:33 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:33 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:33 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:49:34 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:34 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:35 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:35 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:35 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:49:35 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-E9AxlvewhkHXPJwX: 100% - Hoàn thành xử lý tập tin với 35/35 đoạn
2025-05-24 14:49:35 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-E9AxlvewhkHXPJwX_caf4232b-81dc-4f17-9e04-026da2814acf.docx
2025-05-24 14:53:16 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 14:53:21 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 14:53:21 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 14:53:21 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 14:53:21 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 14:54:32 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-07fd...
2025-05-24 14:54:32 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-07fd... (length: 42)
2025-05-24 14:54:32 - services.s3_file_service - INFO - [s3_file_service.py:250] - Created file record fast: file-Jl0nxKe3Js9Sebvv, 1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 14:54:32 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-Jl0nxKe3Js9Sebvv: 0% - Đã tạo file record, chuẩn bị xử lý
2025-05-24 14:54:32 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.2010s
2025-05-24 14:54:32 - api.file_endpoints - INFO - [file_endpoints.py:135] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:54:32 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-Jl0nxKe3Js9Sebvv: 5% - Đang kiểm tra URL
2025-05-24 14:54:33 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-Jl0nxKe3Js9Sebvv: 10% - Đang tải tập tin từ URL
2025-05-24 14:54:33 - core.s3_client - INFO - [s3_client.py:75] - Storage key has CDN prefix: https://cdn.redai.vn/1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 14:54:33 - core.s3_client - INFO - [s3_client.py:131] - Downloading from CDN URL to temp_file-Jl0nxKe3Js9Sebvv_6a305ad0-de91-47dc-b148-ac2095632bbc.docx
2025-05-24 14:54:33 - core.s3_client - INFO - [s3_client.py:145] - Successfully downloaded from CDN to temp_file-Jl0nxKe3Js9Sebvv_6a305ad0-de91-47dc-b148-ac2095632bbc.docx
2025-05-24 14:54:33 - services.s3_file_service - INFO - [s3_file_service.py:194] - Downloaded file to temporary path: temp-s3\temp_file-Jl0nxKe3Js9Sebvv_6a305ad0-de91-47dc-b148-ac2095632bbc.docx
2025-05-24 14:54:33 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-Jl0nxKe3Js9Sebvv: 30% - Đang chuyển đổi tập tin sang Markdown
2025-05-24 14:54:33 - services.markdown_service - INFO - [markdown_service.py:71] - Converting file to Markdown: temp-s3\temp_file-Jl0nxKe3Js9Sebvv_6a305ad0-de91-47dc-b148-ac2095632bbc.docx (type: .docx)
2025-05-24 14:54:33 - services.markdown_service - INFO - [markdown_service.py:200] - Converting Word document to Markdown
2025-05-24 14:54:39 - services.markdown_service - INFO - [markdown_service.py:126] - Successfully converted file to Markdown (59436 characters)
2025-05-24 14:54:39 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-Jl0nxKe3Js9Sebvv: 50% - Đang chia nhỏ nội dung
2025-05-24 14:54:39 - services.chunk_service - INFO - [chunk_service.py:48] - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-24 14:54:39 - services.chunk_service - INFO - [chunk_service.py:63] - MarkdownTextSplitter produced 35 well-sized chunks
2025-05-24 14:54:39 - services.document_chunks_service - INFO - [document_chunks_service.py:67] - Split content into 35 chunks for file file-Jl0nxKe3Js9Sebvv
2025-05-24 14:54:39 - services.document_chunks_service - INFO - [document_chunks_service.py:84] - Sử dụng vector store ID: vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:54:40 - services.document_chunks_service - INFO - [document_chunks_service.py:118] - Successfully saved 35 chunks for file file-Jl0nxKe3Js9Sebvv
2025-05-24 14:54:40 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-Jl0nxKe3Js9Sebvv: 70% - Đang tạo embeddings cho 35 đoạn và lưu vào kho vector vs_e9a11fc2-69eb-41a6-a61d-db2caabf24fe
2025-05-24 14:54:40 - services.document_chunks_service - INFO - [document_chunks_service.py:167] - Creating embeddings for 35 chunks with 5 parallel workers
2025-05-24 14:54:40 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 1 (10 chunks)
2025-05-24 14:54:43 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:54:43 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:54:43 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:54:43 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:54:43 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:54:44 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:54:44 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:54:45 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:54:45 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:54:45 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:54:45 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 2 (10 chunks)
2025-05-24 14:54:46 - core.vector_store - INFO - [vector_store.py:45] - Truncating embedding from 3072 to 2048 dimensions
2025-05-24 14:54:47 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:47 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:47 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:47 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:47 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:47 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:47 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:47 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:47 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:47 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:48 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:48 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:48 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:48 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:48 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:48 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:48 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:48 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:48 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:48 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:48 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:48 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:48 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:49 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:50 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:50 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:50 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:50 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 3 (10 chunks)
2025-05-24 14:54:50 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:50 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:51 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:51 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:51 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:51 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:51 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:51 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:51 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:51 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:52 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:52 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:52 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:52 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:52 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:52 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:52 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:52 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:52 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:52 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:52 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:52 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:52 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:53 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:53 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:53 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:53 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:53 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:54 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:54 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:54 - services.document_chunks_service - INFO - [document_chunks_service.py:172] - Processing batch 4 (5 chunks)
2025-05-24 14:54:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:55 - core.vector_store - ERROR - [vector_store.py:93] - Error getting Gemini embedding: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:55 - core.vector_store - WARNING - [vector_store.py:174] - Gemini embedding failed, falling back to Jina: 429 Resource has been exhausted (e.g. check quota).
2025-05-24 14:54:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:56 - core.vector_store - INFO - [vector_store.py:50] - Padding embedding from 1024 to 2048 dimensions
2025-05-24 14:54:56 - api.file_endpoints - INFO - [file_endpoints.py:100] - Progress update for file-Jl0nxKe3Js9Sebvv: 100% - Hoàn thành xử lý tập tin với 35/35 đoạn
2025-05-24 14:54:56 - utils.cleanup - INFO - [cleanup.py:45] - Removed temporary file: temp_file-Jl0nxKe3Js9Sebvv_6a305ad0-de91-47dc-b148-ac2095632bbc.docx
2025-05-24 14:58:25 - main - INFO - [main.py:176] - Request to /docs completed in 0.0007s
2025-05-24 14:58:25 - main - INFO - [main.py:176] - Request to /openapi.json completed in 0.0407s
2025-05-24 15:41:10 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 19:00:13 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 19:00:13 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 19:00:13 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 19:00:13 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 19:02:45 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 19:03:15 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 19:03:15 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 19:03:15 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 19:03:15 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 19:03:49 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-0648...
2025-05-24 19:03:49 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-0648... (length: 38)
2025-05-24 19:03:49 - services.s3_file_service - INFO - [s3_file_service.py:250] - Created file record fast: file-s0KpAsyJEIMMwInk, 34af6a9f-47c7-4012-8c78-4099b719689b-Example.pdf
2025-05-24 19:03:49 - services.queue_service - INFO - [queue_service.py:206] - Progress update for file-s0KpAsyJEIMMwInk: 0% - Đã tạo file record, chuẩn bị xử lý
2025-05-24 19:03:49 - services.queue_service - INFO - [queue_service.py:299] - Queued file processing task for file-s0KpAsyJEIMMwInk, task ID: a710639b-ab34-4f57-b06d-1f34b328f128
2025-05-24 19:03:49 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.0880s
2025-05-24 19:05:27 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 19:06:10 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 19:06:10 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 19:06:10 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 19:06:10 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 19:07:14 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-0648...
2025-05-24 19:07:14 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-0648... (length: 38)
2025-05-24 19:07:14 - services.s3_file_service - INFO - [s3_file_service.py:250] - Created file record fast: file-DKHnuf9aXzc4AmGv, 1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 19:07:14 - services.queue_service - INFO - [queue_service.py:206] - Progress update for file-DKHnuf9aXzc4AmGv: 0% - Đã tạo file record, chuẩn bị xử lý
2025-05-24 19:07:14 - services.queue_service - INFO - [queue_service.py:299] - Queued file processing task for file-DKHnuf9aXzc4AmGv, task ID: 221659f5-e34e-42bd-835d-861131c59ecb
2025-05-24 19:07:14 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.0502s
2025-05-24 19:09:36 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 19:09:36 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 19:09:36 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 19:09:36 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 19:09:41 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 19:09:41 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 19:09:41 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 19:09:41 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 19:10:48 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-0648...
2025-05-24 19:10:48 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-0648... (length: 42)
2025-05-24 19:10:48 - services.api_key_service - WARNING - [api_key_service.py:99] - API key not found or not active: redai-0648...
2025-05-24 19:10:48 - api.middleware.api_key_auth - WARNING - [api_key_auth.py:121] - Invalid API key attempt from 127.0.0.1: redai-0648...
2025-05-24 19:10:48 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.0181s
2025-05-24 19:11:07 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 19:11:10 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 19:11:10 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 19:11:11 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 19:11:11 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-24 19:11:20 - services.api_key_service - INFO - [api_key_service.py:38] - Created new API key: redai-9bb6...
2025-05-24 19:11:20 - main - INFO - [main.py:176] - Request to /api/auth/keys completed in 0.0974s
2025-05-24 19:11:41 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-9bb6...
2025-05-24 19:11:41 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-9bb6... (length: 42)
2025-05-24 19:11:41 - services.s3_file_service - INFO - [s3_file_service.py:250] - Created file record fast: file-275QSXVm1XZ5e1yp, dummy.pdf
2025-05-24 19:11:41 - services.queue_service - INFO - [queue_service.py:309] - 🎯 QUEUEING FILE PROCESSING TASK
2025-05-24 19:11:41 - services.queue_service - INFO - [queue_service.py:310] - 📋 Parameters: file_id=file-275QSXVm1XZ5e1yp, url=https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf
2025-05-24 19:11:41 - services.queue_service - INFO - [queue_service.py:311] - 📋 Chunk settings: size=1000, overlap=50
2025-05-24 19:11:41 - services.queue_service - INFO - [queue_service.py:312] - 📋 Vector store: vs_test
2025-05-24 19:11:41 - services.queue_service - INFO - [queue_service.py:315] - 📤 Adding task to Huey queue...
2025-05-24 19:11:41 - services.queue_service - INFO - [queue_service.py:228] - Progress update for file-275QSXVm1XZ5e1yp: 0% - Đã tạo file record, chuẩn bị xử lý
2025-05-24 19:11:41 - services.queue_service - INFO - [queue_service.py:327] - ✅ SUCCESSFULLY QUEUED task for file-275QSXVm1XZ5e1yp, task ID: 97210c43-4f35-435d-9263-b475175e09fd
2025-05-24 19:11:41 - services.queue_service - INFO - [queue_service.py:328] - 📊 Current queue size: 1
2025-05-24 19:11:41 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.1600s
2025-05-24 19:11:41 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/file-275QSXVm1XZ5e1yp/progress with API key: redai-9bb6...
2025-05-24 19:11:41 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-9bb6... (length: 42)
2025-05-24 19:11:41 - main - INFO - [main.py:176] - Request to /api/files/file-275QSXVm1XZ5e1yp/progress completed in 0.0324s
2025-05-24 19:11:43 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/file-275QSXVm1XZ5e1yp/progress with API key: redai-9bb6...
2025-05-24 19:11:43 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-9bb6... (length: 42)
2025-05-24 19:11:43 - main - INFO - [main.py:176] - Request to /api/files/file-275QSXVm1XZ5e1yp/progress completed in 0.0774s
2025-05-24 19:11:45 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/file-275QSXVm1XZ5e1yp/progress with API key: redai-9bb6...
2025-05-24 19:11:45 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-9bb6... (length: 42)
2025-05-24 19:11:45 - main - INFO - [main.py:176] - Request to /api/files/file-275QSXVm1XZ5e1yp/progress completed in 0.0544s
2025-05-24 19:11:47 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/file-275QSXVm1XZ5e1yp/progress with API key: redai-9bb6...
2025-05-24 19:11:47 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-9bb6... (length: 42)
2025-05-24 19:11:47 - main - INFO - [main.py:176] - Request to /api/files/file-275QSXVm1XZ5e1yp/progress completed in 0.0519s
2025-05-24 19:12:25 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/process-url with API key: redai-9bb6...
2025-05-24 19:12:25 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-9bb6... (length: 42)
2025-05-24 19:12:25 - services.s3_file_service - INFO - [s3_file_service.py:250] - Created file record fast: file-ldIgmjKYTWnuq2VY, dummy.pdf
2025-05-24 19:12:25 - services.queue_service - INFO - [queue_service.py:309] - 🎯 QUEUEING FILE PROCESSING TASK
2025-05-24 19:12:25 - services.queue_service - INFO - [queue_service.py:310] - 📋 Parameters: file_id=file-ldIgmjKYTWnuq2VY, url=https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf
2025-05-24 19:12:25 - services.queue_service - INFO - [queue_service.py:311] - 📋 Chunk settings: size=1000, overlap=50
2025-05-24 19:12:25 - services.queue_service - INFO - [queue_service.py:312] - 📋 Vector store: vs_test
2025-05-24 19:12:25 - services.queue_service - INFO - [queue_service.py:315] - 📤 Adding task to Huey queue...
2025-05-24 19:12:25 - services.queue_service - INFO - [queue_service.py:228] - Progress update for file-ldIgmjKYTWnuq2VY: 0% - Đã tạo file record, chuẩn bị xử lý
2025-05-24 19:12:25 - services.queue_service - INFO - [queue_service.py:327] - ✅ SUCCESSFULLY QUEUED task for file-ldIgmjKYTWnuq2VY, task ID: 90985caf-c308-479a-a440-ccb03eca1aa2
2025-05-24 19:12:25 - services.queue_service - INFO - [queue_service.py:328] - 📊 Current queue size: 1
2025-05-24 19:12:25 - main - INFO - [main.py:176] - Request to /api/files/process-url completed in 0.2175s
2025-05-24 19:12:25 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/file-ldIgmjKYTWnuq2VY/progress with API key: redai-9bb6...
2025-05-24 19:12:25 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-9bb6... (length: 42)
2025-05-24 19:12:25 - main - INFO - [main.py:176] - Request to /api/files/file-ldIgmjKYTWnuq2VY/progress completed in 0.0706s
2025-05-24 19:12:27 - api.middleware.api_key_auth - INFO - [api_key_auth.py:105] - Request to /api/files/file-ldIgmjKYTWnuq2VY/progress with API key: redai-9bb6...
2025-05-24 19:12:27 - services.api_key_service - INFO - [api_key_service.py:91] - Verifying API key: redai-9bb6... (length: 42)
2025-05-24 19:12:27 - main - INFO - [main.py:176] - Request to /api/files/file-ldIgmjKYTWnuq2VY/progress completed in 0.0813s
2025-05-24 19:16:08 - main - INFO - [main.py:114] - Shutting down the application
2025-05-24 19:16:11 - main - INFO - [main.py:97] - Starting up the application
2025-05-24 19:16:11 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-24 19:16:11 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-24 19:16:11 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-27 08:48:52 - main - INFO - [main.py:97] - Starting up the application
2025-05-27 08:48:52 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-27 08:48:56 - core.database - ERROR - [database.py:57] - Error initializing database: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused (0x0000274D/10061)
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused (0x0000274D/10061)
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-27 08:48:56 - main - ERROR - [main.py:108] - Error initializing database: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused (0x0000274D/10061)
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused (0x0000274D/10061)
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-27 08:54:42 - main - INFO - [main.py:97] - Starting up the application
2025-05-27 08:54:42 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-27 08:54:43 - __main__ - ERROR - [run_all.py:65] - ❌ Worker error: Consumer.run() got an unexpected keyword argument 'workers'
2025-05-27 08:54:44 - __main__ - ERROR - [run_all.py:65] - ❌ Worker error: Consumer.run() got an unexpected keyword argument 'workers'
2025-05-27 08:54:45 - __main__ - ERROR - [run_all.py:65] - ❌ Worker error: Consumer.run() got an unexpected keyword argument 'workers'
2025-05-27 08:54:46 - __main__ - ERROR - [run_all.py:65] - ❌ Worker error: Consumer.run() got an unexpected keyword argument 'workers'
2025-05-27 08:54:46 - core.database - ERROR - [database.py:57] - Error initializing database: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused (0x0000274D/10061)
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused (0x0000274D/10061)
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-27 08:54:46 - main - ERROR - [main.py:108] - Error initializing database: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused (0x0000274D/10061)
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused (0x0000274D/10061)
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-27 08:57:47 - main - INFO - [main.py:97] - Starting up the application
2025-05-27 08:57:47 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-27 08:57:51 - core.database - ERROR - [database.py:57] - Error initializing database: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused (0x0000274D/10061)
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused (0x0000274D/10061)
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-27 08:57:51 - main - ERROR - [main.py:108] - Error initializing database: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused (0x0000274D/10061)
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused (0x0000274D/10061)
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-27 08:57:51 - __main__ - INFO - [run_all.py:74] - Stopping worker subprocess...
2025-05-27 08:57:51 - __main__ - INFO - [run_all.py:78] - Worker stopped successfully
2025-05-27 08:59:20 - main - INFO - [main.py:97] - Starting up the application
2025-05-27 08:59:20 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-27 08:59:20 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-27 08:59:20 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-27 08:59:58 - main - INFO - [main.py:176] - Request to /docs completed in 0.0007s
2025-05-27 08:59:59 - main - INFO - [main.py:176] - Request to /openapi.json completed in 0.0147s
2025-05-27 09:02:58 - main - INFO - [main.py:114] - Shutting down the application
2025-05-27 09:02:58 - __main__ - INFO - [run_all.py:89] - Received signal 2, shutting down...
2025-05-27 09:02:58 - __main__ - INFO - [run_all.py:74] - Stopping worker subprocess...
2025-05-27 09:02:58 - __main__ - INFO - [run_all.py:78] - Worker stopped successfully
2025-05-27 09:02:58 - __main__ - INFO - [run_all.py:91] - Shutdown complete
2025-05-27 09:03:07 - main - INFO - [main.py:97] - Starting up the application
2025-05-27 09:03:07 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-27 09:03:07 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-27 09:03:07 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-27 09:03:12 - main - INFO - [main.py:114] - Shutting down the application
2025-05-27 09:03:12 - __main__ - INFO - [run_all.py:90] - Received signal 2, shutting down...
2025-05-27 09:03:12 - __main__ - INFO - [run_all.py:75] - Stopping worker subprocess...
2025-05-27 09:03:12 - __main__ - INFO - [run_all.py:79] - Worker stopped successfully
2025-05-27 09:03:12 - __main__ - INFO - [run_all.py:92] - Shutdown complete
2025-05-27 09:09:44 - main - INFO - [main.py:97] - Starting up the application
2025-05-27 09:09:44 - main - INFO - [main.py:101] - Ensured temp directory exists: temp-s3
2025-05-27 09:09:45 - core.database - INFO - [database.py:55] - Database tables created successfully
2025-05-27 09:09:45 - main - INFO - [main.py:106] - Database initialized successfully
2025-05-27 09:09:55 - main - INFO - [main.py:114] - Shutting down the application
2025-05-27 09:09:55 - __main__ - INFO - [run_all.py:90] - Received signal 2, shutting down...
2025-05-27 09:09:55 - __main__ - INFO - [run_all.py:75] - Stopping worker subprocess...
2025-05-27 09:09:55 - __main__ - INFO - [run_all.py:79] - Worker stopped successfully
2025-05-27 09:09:55 - __main__ - INFO - [run_all.py:92] - Shutdown complete
